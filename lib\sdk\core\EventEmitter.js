/**
 * 事件发射器类
 * 提供基本的事件订阅和发布功能
 */
export class EventEmitter {
  constructor() {
    this.events = {};
  }

  /**
   * 订阅事件
   * @param {string} event - 事件名称
   * @param {Function} listener - 事件监听器
   * @returns {Function} - 取消订阅的函数
   */
  on(event, listener) {
    if (!this.events[event]) {
      this.events[event] = [];
    }

    this.events[event].push(listener);

    return () => this.off(event, listener);
  }

  /**
   * 取消订阅事件
   * @param {string} event - 事件名称
   * @param {Function} listener - 要移除的监听器
   */
  off(event, listener) {
    if (!this.events[event]) return;

    this.events[event] = this.events[event].filter(l => l !== listener);
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {Object} data - 事件数据
   */
  emit(event, data = {}) {
    if (!this.events[event]) return;

    this.events[event].forEach(listener => {
      listener(data);
    });
  }

  /**
   * 只订阅一次事件
   * @param {string} event - 事件名称
   * @param {Function} listener - 事件监听器
   */
  once(event, listener) {
    const remove = this.on(event, data => {
      remove();
      listener(data);
    });
  }

  /**
   * 清除所有事件监听器
   * @param {string} [event] - 可选的事件名称，如果提供则只清除该事件的监听器
   */
  clear(event) {
    if (event) {
      this.events[event] = [];
    } else {
      this.events = {};
    }
  }
}