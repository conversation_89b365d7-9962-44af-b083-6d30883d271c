import * as THREE from 'three';

/**
 * 骨骼绑定工具类
 * 用于将衣服的骨骼绑定到角色模型的骨骼上
 */
export class SkeletonBinder {
    constructor() {
        this.boundClothes = new Map(); // 存储已绑定的衣服
    }

    /**
     * 绑定衣服到角色骨骼
     * @param {THREE.Object3D} characterModel - 角色模型
     * @param {THREE.Object3D} clothModel - 衣服模型
     * @param {Object} options - 绑定选项
     * @returns {boolean} - 绑定是否成功
     */
    bindClothToCharacter(characterModel, clothModel, options = {}) {
        try {
            console.log('开始绑定衣服骨骼...');
            
            // 1. 获取角色的骨骼系统
            const characterSkeleton = this.findSkeleton(characterModel);
            if (!characterSkeleton) {
                console.warn('角色模型中未找到骨骼系统');
                return false;
            }

            console.log('找到角色骨骼:', characterSkeleton);

            // 2. 确保衣服模型的变换已经应用
            clothModel.updateMatrixWorld(true);

            // 3. 处理衣服中的所有SkinnedMesh
            let bindCount = 0;
            clothModel.traverse((node) => {
                if (node.isSkinnedMesh) {
                    console.log('处理衣服SkinnedMesh:', node.name);
                    console.log('SkinnedMesh当前缩放:', node.scale);
                    console.log('SkinnedMesh世界矩阵:', node.matrixWorld);

                    // 尝试绑定到角色骨骼
                    if (this.bindSkinnedMeshToSkeleton(node, characterSkeleton)) {
                        bindCount++;
                        console.log(`成功绑定: ${node.name}`);
                        console.log('绑定后SkinnedMesh缩放:', node.scale);
                    } else {
                        console.warn(`绑定失败: ${node.name}`);
                    }
                }
            });

            if (bindCount > 0) {
                // 存储绑定信息
                this.boundClothes.set(clothModel.uuid, {
                    clothModel,
                    characterModel,
                    characterSkeleton,
                    bindCount
                });
                
                console.log(`骨骼绑定完成，成功绑定 ${bindCount} 个SkinnedMesh`);
                return true;
            } else {
                console.warn('没有成功绑定任何SkinnedMesh');
                return false;
            }

        } catch (error) {
            console.error('骨骼绑定过程中出错:', error);
            return false;
        }
    }

    /**
     * 查找模型中的骨骼系统
     * @param {THREE.Object3D} model - 模型对象
     * @returns {THREE.Skeleton|null} - 骨骼系统
     */
    findSkeleton(model) {
        let skeleton = null;
        
        model.traverse((node) => {
            if (node.isSkinnedMesh && node.skeleton) {
                skeleton = node.skeleton;
                console.log('找到SkinnedMesh骨骼:', node.name, skeleton);
                return; // 找到第一个就返回
            }
        });

        return skeleton;
    }

    /**
     * 将SkinnedMesh绑定到指定骨骼
     * @param {THREE.SkinnedMesh} skinnedMesh - 蒙皮网格
     * @param {THREE.Skeleton} targetSkeleton - 目标骨骼系统
     * @returns {boolean} - 绑定是否成功
     */
    bindSkinnedMeshToSkeleton(skinnedMesh, targetSkeleton) {
        try {
            // 检查骨骼兼容性
            if (!this.checkSkeletonCompatibility(skinnedMesh.skeleton, targetSkeleton)) {
                console.warn(`骨骼不兼容: ${skinnedMesh.name}`);
                // 即使不完全兼容，也尝试绑定
            }

            // 创建骨骼映射
            const boneMapping = this.createBoneMapping(skinnedMesh.skeleton, targetSkeleton);
            
            if (boneMapping.size === 0) {
                console.warn(`无法创建骨骼映射: ${skinnedMesh.name}`);
                return false;
            }

            // 重新绑定骨骼
            const newBones = [];
            const originalBones = skinnedMesh.skeleton.bones;
            
            for (let i = 0; i < originalBones.length; i++) {
                const originalBone = originalBones[i];
                const mappedBone = boneMapping.get(originalBone.name) || boneMapping.get(originalBone.uuid);
                
                if (mappedBone) {
                    newBones.push(mappedBone);
                } else {
                    // 如果找不到对应骨骼，使用原骨骼
                    console.warn(`未找到对应骨骼: ${originalBone.name}`);
                    newBones.push(originalBone);
                }
            }

            // 保存原始变换信息，优先使用userData中保存的信息
            const originalScale = skinnedMesh.userData.originalScale || skinnedMesh.scale.clone();
            const originalPosition = skinnedMesh.userData.originalPosition || skinnedMesh.position.clone();
            const originalRotation = skinnedMesh.userData.originalRotation || skinnedMesh.rotation.clone();

            console.log('绑定前SkinnedMesh变换:', {
                scale: originalScale,
                position: originalPosition,
                rotation: originalRotation
            });

            // 创建新的骨骼系统
            const newSkeleton = new THREE.Skeleton(newBones);

            // 更新SkinnedMesh的骨骼绑定
            // 使用当前的世界矩阵进行绑定（与test-skeleton-binding.html一致）
            skinnedMesh.bind(newSkeleton, skinnedMesh.matrixWorld);

            // 确保变换信息保持不变
            skinnedMesh.scale.copy(originalScale);
            skinnedMesh.position.copy(originalPosition);
            skinnedMesh.rotation.copy(originalRotation);
            skinnedMesh.updateMatrix();

            console.log('绑定后SkinnedMesh变换:', {
                scale: skinnedMesh.scale,
                position: skinnedMesh.position,
                rotation: skinnedMesh.rotation
            });
            
            console.log(`成功重新绑定骨骼: ${skinnedMesh.name}, 映射了 ${boneMapping.size} 个骨骼`);
            return true;

        } catch (error) {
            console.error(`绑定SkinnedMesh失败: ${skinnedMesh.name}`, error);
            return false;
        }
    }

    /**
     * 检查两个骨骼系统的兼容性
     * @param {THREE.Skeleton} sourceSkeleton - 源骨骼系统
     * @param {THREE.Skeleton} targetSkeleton - 目标骨骼系统
     * @returns {boolean} - 是否兼容
     */
    checkSkeletonCompatibility(sourceSkeleton, targetSkeleton) {
        const sourceNames = sourceSkeleton.bones.map(bone => bone.name);
        const targetNames = targetSkeleton.bones.map(bone => bone.name);
        
        // 计算匹配的骨骼数量
        const matchCount = sourceNames.filter(name => targetNames.includes(name)).length;
        const compatibilityRatio = matchCount / sourceNames.length;
        
        console.log(`骨骼兼容性: ${(compatibilityRatio * 100).toFixed(1)}% (${matchCount}/${sourceNames.length})`);
        
        return compatibilityRatio > 0.5; // 超过50%匹配认为兼容
    }

    /**
     * 创建骨骼映射关系
     * @param {THREE.Skeleton} sourceSkeleton - 源骨骼系统
     * @param {THREE.Skeleton} targetSkeleton - 目标骨骼系统
     * @returns {Map} - 骨骼映射关系
     */
    createBoneMapping(sourceSkeleton, targetSkeleton) {
        const mapping = new Map();
        const targetBonesByName = new Map();
        
        // 建立目标骨骼的名称索引
        targetSkeleton.bones.forEach(bone => {
            targetBonesByName.set(bone.name, bone);
        });

        // 创建映射关系
        sourceSkeleton.bones.forEach(sourceBone => {
            const targetBone = targetBonesByName.get(sourceBone.name);
            if (targetBone) {
                mapping.set(sourceBone.name, targetBone);
                mapping.set(sourceBone.uuid, targetBone);
            }
        });

        return mapping;
    }

    /**
     * 解除衣服的骨骼绑定
     * @param {THREE.Object3D} clothModel - 衣服模型
     */
    unbindCloth(clothModel) {
        const bindInfo = this.boundClothes.get(clothModel.uuid);
        if (bindInfo) {
            console.log('解除衣服骨骼绑定:', clothModel.uuid);
            this.boundClothes.delete(clothModel.uuid);
        }
    }

    /**
     * 获取绑定信息
     * @returns {Map} - 绑定信息映射
     */
    getBoundClothes() {
        return this.boundClothes;
    }

    /**
     * 清除所有绑定
     */
    clearAllBindings() {
        this.boundClothes.clear();
        console.log('已清除所有骨骼绑定');
    }
}
