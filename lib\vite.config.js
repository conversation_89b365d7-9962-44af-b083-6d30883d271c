import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import pkg from './package.json' assert { type: 'json' };

export default defineConfig({
  plugins: [vue()],
  build: {
    lib: {
      entry: resolve(__dirname, 'index.js'),
      name: 'AvatarJsSDK',
      fileName: (format) => `avatar-js-sdk.${format}.js`,
      formats: ['es', 'cjs', 'umd']
    },
    rollupOptions: {
      external: ['vue', 'three'],
      output: {
        globals: {
          vue: 'Vue',
          three: 'THREE'
        },
        exports: 'named',
        banner: `/*!
 * ${pkg.name} v${pkg.version}
 * (c) ${new Date().getFullYear()}
 * @license ${pkg.license}
 */`,
        assetFileNames: (assetInfo) => {
          if (assetInfo.name === 'style.css') {
            return 'style.css';
          }
          return assetInfo.name;
        }
      }
    },
    sourcemap: true,
    outDir: 'dist',
    emptyOutDir: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '.')
    }
  }
});