<template>
    <div class="avatar-viewer">
        <canvas ref="avatarViewCanvas" class="avatar-view-canvas"></canvas>
    </div>
</template>

<script setup>
import { AvatarSDK, createAvatarSDK } from '@/sdk';
import { ref, reactive, onMounted, onUnmounted, setDevtoolsHook } from 'vue'

const avatarViewCanvas = ref(null);

const props = defineProps({
    debug: {
        type: Boolean,
        defalut: false
    },
    showControls: {
        type: Boolean,
        defalut: true
    }

});

/** @type {AvatarSDK} */
let avatarSDK

onMounted(async () => {
    try {
        avatarSDK = createAvatarSDK({debug:props.debug,showControls:props.showControls});
        await avatarSDK.init(avatarViewCanvas.value);
        await avatarSDK.loadModel("models/AFdBdA001.fbx")
        // await avatarSDK.playAnimation("anim/Locking Hip Hop Dance.fbx");
    } catch (error) {
        console.log("init avatar error....", error);
    }

})

onUnmounted(() => {
})

</script>

<style scoped>
.avatar-viewer {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.avatar-view-canvas {
    width: 100%;
    height: 100%;
    display: block;
}
</style>