<template>
    <div class="avatar-viewer">
        <canvas ref="avatarViewCanvas" class="avatar-view-canvas"></canvas>
    </div>
</template>

<script setup>
import { AvatarSDK, createAvatarSDK } from '@/sdk';
import { ref, onMounted, onUnmounted } from 'vue'

const avatarViewCanvas = ref(null);

const props = defineProps({
    debug: {
        type: Boolean,
        defalut: false
    },
    showControls: {
        type: Boolean,
        defalut: true
    }

});

/** @type {AvatarSDK} */
let avatarSDK

onMounted(async () => {
    try {
        avatarSDK = createAvatarSDK({debug:props.debug,showControls:props.showControls});
        await avatarSDK.init(avatarViewCanvas.value);

        // 1. 加载基础角色模型
        console.log("Loading base model...");
        await avatarSDK.loadModel("models/afdbda001.glb");

        // 2. 加载衣服
        console.log("Loading clothes...");
        await avatarSDK.loadCloth("AFdWbA001_wb");

        // 3. 播放idle动画
        console.log("Playing idle animation...");
        await avatarSDK.playIdleAnimation();

        console.log("Avatar setup complete!");
    } catch (error) {
        console.log("init avatar error....", error);
    }

})

onUnmounted(() => {
})

</script>

<style scoped>
.avatar-viewer {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.avatar-view-canvas {
    width: 100%;
    height: 100%;
    display: block;
}
</style>