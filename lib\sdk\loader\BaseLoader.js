/**
 * 基础加载器类
 * 提供通用的资源加载功能
 */
export class BaseLoader {
  constructor() {
    this.cache = new Map();
    this.loading = new Map();
  }

  /**
   * 加载资源
   * @param {string} url - 资源URL
   * @param {Object} options - 加载选项
   * @returns {Promise<any>} - 加载的资源
   */
  async load(url, options = {}) {
    // 如果已经在加载中，返回正在加载的Promise
    if (this.loading.has(url)) {
      return this.loading.get(url);
    }

    // 如果已经加载过且不需要重新加载，直接返回缓存
    if (!options.forceReload && this.cache.has(url)) {
      return this.cache.get(url);
    }

    // 创建新的加载Promise
    const loadPromise = this._loadResource(url, options);
    this.loading.set(url, loadPromise);

    try {
      const resource = await loadPromise;
      this.cache.set(url, resource);
      return resource;
    } finally {
      this.loading.delete(url);
    }
  }

  /**
   * 实际加载资源的方法，需要被子类重写
   * @param {string} url - 资源URL
   * @param {Object} options - 加载选项
   * @returns {Promise<any>} - 加载的资源
   * @protected
   */
  async _loadResource(url, options) {
    throw new Error('_loadResource方法必须由子类实现');
  }

  /**
   * 清除缓存
   * @param {string} [url] - 可选的URL，如果提供则只清除该URL的缓存
   */
  clearCache(url) {
    if (url) {
      this.cache.delete(url);
    } else {
      this.cache.clear();
    }
  }
}