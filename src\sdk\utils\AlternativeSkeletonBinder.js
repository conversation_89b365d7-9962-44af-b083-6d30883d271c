import * as THREE from 'three';

/**
 * 替代骨骼绑定方案
 * 通过直接复制骨骼变换而不是重新绑定来实现同步
 */
export class AlternativeSkeletonBinder {
    constructor() {
        this.boundClothes = new Map();
        this.animationCallbacks = [];
    }

    /**
     * 绑定衣服到角色（不重新绑定骨骼，而是同步骨骼变换）
     * @param {THREE.Object3D} characterModel - 角色模型
     * @param {THREE.Object3D} clothModel - 衣服模型
     * @param {Object} options - 绑定选项
     * @returns {boolean} - 绑定是否成功
     */
    bindClothToCharacter(characterModel, clothModel, options = {}) {
        try {
            console.log('🔄 开始替代骨骼绑定方案...');
            
            // 1. 获取角色的骨骼系统
            const characterSkeleton = this.findSkeleton(characterModel);
            if (!characterSkeleton) {
                console.warn('角色模型中未找到骨骼系统');
                return false;
            }

            // 2. 获取衣服的骨骼系统
            const clothSkeletons = this.findAllSkeletons(clothModel);
            if (clothSkeletons.length === 0) {
                console.warn('衣服模型中未找到骨骼系统');
                return false;
            }

            // 3. 创建骨骼映射关系
            const bindingInfo = {
                characterModel,
                clothModel,
                characterSkeleton,
                clothSkeletons,
                boneMappings: []
            };

            // 4. 为每个衣服骨骼系统创建映射
            for (let clothSkeleton of clothSkeletons) {
                const mapping = this.createBoneMapping(characterSkeleton, clothSkeleton);
                if (mapping.size > 0) {
                    bindingInfo.boneMappings.push({
                        clothSkeleton,
                        mapping
                    });
                    console.log(`✅ 创建骨骼映射: ${mapping.size} 个骨骼`);
                }
            }

            if (bindingInfo.boneMappings.length > 0) {
                // 5. 存储绑定信息
                this.boundClothes.set(clothModel.uuid, bindingInfo);
                
                // 6. 创建同步回调
                const syncCallback = () => this.syncBoneTransforms(bindingInfo);
                this.animationCallbacks.push(syncCallback);
                
                console.log(`✅ 替代骨骼绑定完成，映射了 ${bindingInfo.boneMappings.length} 个骨骼系统`);
                return true;
            } else {
                console.warn('未能创建任何骨骼映射');
                return false;
            }

        } catch (error) {
            console.error('替代骨骼绑定过程中出错:', error);
            return false;
        }
    }

    /**
     * 查找模型中的第一个骨骼系统
     * @param {THREE.Object3D} model - 模型对象
     * @returns {THREE.Skeleton|null} - 骨骼系统
     */
    findSkeleton(model) {
        let skeleton = null;
        
        model.traverse((node) => {
            if (node.isSkinnedMesh && node.skeleton && !skeleton) {
                skeleton = node.skeleton;
            }
        });

        return skeleton;
    }

    /**
     * 查找模型中的所有骨骼系统
     * @param {THREE.Object3D} model - 模型对象
     * @returns {Array<THREE.Skeleton>} - 骨骼系统数组
     */
    findAllSkeletons(model) {
        const skeletons = [];
        
        model.traverse((node) => {
            if (node.isSkinnedMesh && node.skeleton) {
                // 避免重复添加相同的骨骼系统
                if (!skeletons.find(s => s.uuid === node.skeleton.uuid)) {
                    skeletons.push(node.skeleton);
                }
            }
        });

        return skeletons;
    }

    /**
     * 创建骨骼映射关系
     * @param {THREE.Skeleton} characterSkeleton - 角色骨骼系统
     * @param {THREE.Skeleton} clothSkeleton - 衣服骨骼系统
     * @returns {Map} - 骨骼映射关系
     */
    createBoneMapping(characterSkeleton, clothSkeleton) {
        const mapping = new Map();
        const characterBonesByName = new Map();
        
        // 建立角色骨骼的名称索引
        characterSkeleton.bones.forEach(bone => {
            characterBonesByName.set(bone.name, bone);
        });

        // 创建映射关系
        clothSkeleton.bones.forEach((clothBone, index) => {
            const characterBone = characterBonesByName.get(clothBone.name);
            if (characterBone) {
                mapping.set(index, {
                    clothBone,
                    characterBone,
                    name: clothBone.name
                });
            }
        });

        return mapping;
    }

    /**
     * 同步骨骼变换
     * @param {Object} bindingInfo - 绑定信息
     */
    syncBoneTransforms(bindingInfo) {
        try {
            for (let mappingInfo of bindingInfo.boneMappings) {
                const { clothSkeleton, mapping } = mappingInfo;
                
                // 同步每个映射的骨骼变换
                mapping.forEach((boneInfo, clothBoneIndex) => {
                    const { clothBone, characterBone } = boneInfo;
                    
                    // 复制角色骨骼的变换到衣服骨骼
                    clothBone.position.copy(characterBone.position);
                    clothBone.rotation.copy(characterBone.rotation);
                    clothBone.scale.copy(characterBone.scale);
                    clothBone.updateMatrix();
                });
                
                // 更新骨骼系统
                clothSkeleton.update();
            }
        } catch (error) {
            console.error('同步骨骼变换时出错:', error);
        }
    }

    /**
     * 更新所有绑定的衣服（在动画循环中调用）
     */
    update() {
        this.animationCallbacks.forEach(callback => {
            try {
                callback();
            } catch (error) {
                console.error('执行动画回调时出错:', error);
            }
        });
    }

    /**
     * 解除衣服的骨骼绑定
     * @param {THREE.Object3D} clothModel - 衣服模型
     */
    unbindCloth(clothModel) {
        const bindInfo = this.boundClothes.get(clothModel.uuid);
        if (bindInfo) {
            console.log('解除替代骨骼绑定:', clothModel.uuid);
            this.boundClothes.delete(clothModel.uuid);
            
            // 移除对应的回调
            this.animationCallbacks = this.animationCallbacks.filter(callback => {
                // 这里需要更复杂的逻辑来识别特定的回调
                return true; // 简化处理
            });
        }
    }

    /**
     * 获取绑定信息
     * @returns {Map} - 绑定信息映射
     */
    getBoundClothes() {
        return this.boundClothes;
    }

    /**
     * 清除所有绑定
     */
    clearAllBindings() {
        this.boundClothes.clear();
        this.animationCallbacks = [];
        console.log('已清除所有替代骨骼绑定');
    }
}
