{"name": "avatar-js-sdk", "version": "1.0.0", "description": "基于Three.js的3D头像展示SDK，提供简单易用的API和Vue组件", "type": "module", "main": "./dist/avatar-js-sdk.cjs.js", "module": "./dist/avatar-js-sdk.es.js", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": "./dist/avatar-js-sdk.es.js", "require": "./dist/avatar-js-sdk.cjs.js"}, "./style.css": "./dist/style.css"}, "files": ["dist", "index.d.ts", "README.md"], "sideEffects": false, "scripts": {"build": "vite build", "dev": "vite build --watch", "prepublishOnly": "npm run build && npm run check", "check": "node publish-check.cjs", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["three.js", "3d", "avatar", "vue", "sdk", "gltf", "fbx", "animation"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/avatar-js-sdk.git"}, "homepage": "https://github.com/yourusername/avatar-js-sdk#readme", "peerDependencies": {"vue": "^3.0.0"}, "dependencies": {"three": "^0.158.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "vite": "^4.4.5"}}