<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GLB Loader Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>GLB Loader Test</h1>
        <p>This page tests the GLB loader functionality.</p>
        
        <button onclick="testFormatDetection()">Test Format Detection</button>
        <button onclick="testGLBLoading()">Test GLB Loading</button>
        <button onclick="clearLog()">Clear Log</button>
        
        <div id="log" class="log">Click a button to start testing...</div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
        import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';

        const logElement = document.getElementById('log');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        window.clearLog = function() {
            logElement.textContent = '';
        }

        // 模拟ModelLoader的格式检测功能
        function getFileFormat(path) {
            const extension = path.toLowerCase().split('.').pop();
            switch (extension) {
                case 'fbx':
                    return 'fbx';
                case 'glb':
                case 'gltf':
                    return 'gltf';
                default:
                    throw new Error(`Unsupported file format: ${extension}`);
            }
        }

        window.testFormatDetection = function() {
            log('Starting format detection test...');
            
            try {
                const fbxFormat = getFileFormat('models/test.fbx');
                log(`✅ FBX format detected: ${fbxFormat}`);
                
                const glbFormat = getFileFormat('models/test.glb');
                log(`✅ GLB format detected: ${glbFormat}`);
                
                const gltfFormat = getFileFormat('models/test.gltf');
                log(`✅ GLTF format detected: ${gltfFormat}`);
                
                log('✅ Format detection test passed!');
            } catch (error) {
                log(`❌ Format detection test failed: ${error.message}`);
            }
        }

        window.testGLBLoading = function() {
            log('Starting GLB loading test...');
            
            const loader = new GLTFLoader();
            
            loader.load(
                '/models/afdbda001.glb',
                function(gltf) {
                    log('✅ GLB model loaded successfully!');
                    log(`Model scene type: ${gltf.scene.type}`);
                    log(`Model children count: ${gltf.scene.children.length}`);
                    
                    // 遍历模型的子对象
                    gltf.scene.traverse((child) => {
                        if (child.isMesh) {
                            log(`Found mesh: ${child.name || 'unnamed'} (geometry: ${child.geometry.type})`);
                        }
                    });
                    
                    log('✅ GLB loading test completed successfully!');
                },
                function(progress) {
                    const percent = (progress.loaded / progress.total * 100).toFixed(2);
                    log(`Loading progress: ${percent}%`);
                },
                function(error) {
                    log(`❌ GLB loading test failed: ${error.message}`);
                    console.error('GLB loading error:', error);
                }
            );
        }

        log('GLB Loader Test page loaded. Ready for testing.');
    </script>
</body>
</html>
