<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Avatar JS SDK - 独立库使用示例</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .avatar-container {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Avatar JS SDK - 独立库使用示例</h1>
        <p>这是一个完全独立的Avatar JS SDK库的使用示例</p>
        
        <div id="app">
            <div class="avatar-container">
                <avatar-view 
                    ref="avatarViewRef"
                    :debug="debug" 
                    :show-controls="showControls"
                    @sdk-ready="handleSDKReady"
                    @model-loaded="handleModelLoaded"
                    @animation-played="handleAnimationPlayed"
                    @error="handleError"
                ></avatar-view>
            </div>
            
            <div class="controls">
                <button @click="loadModel">加载模型</button>
                <button @click="playAnimation">播放动画</button>
                <button @click="toggleDebug">切换调试模式</button>
                <button @click="toggleControls">切换控制器</button>
            </div>
            
            <div class="status">{{ status }}</div>
        </div>
    </div>

    <!-- 引入Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- 引入Three.js -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <!-- 引入Avatar JS SDK -->
    <script src="./dist/avatar-js-sdk.umd.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="./dist/style.css">

    <script>
        const { createApp, ref } = Vue;
        const { plugin: AvatarJsSDK } = window.AvatarJsSDK;

        createApp({
            setup() {
                const avatarViewRef = ref(null);
                const debug = ref(false);
                const showControls = ref(true);
                const status = ref('等待SDK初始化...');

                function updateStatus(message) {
                    const timestamp = new Date().toLocaleTimeString();
                    status.value += `\n[${timestamp}] ${message}`;
                }

                function handleSDKReady(sdk) {
                    updateStatus('SDK准备就绪');
                    console.log('SDK准备就绪', sdk);
                }

                function handleModelLoaded(model) {
                    updateStatus('模型加载完成');
                    console.log('模型加载完成', model);
                }

                function handleAnimationPlayed(animation) {
                    updateStatus('动画播放');
                    console.log('动画播放', animation);
                }

                function handleError(error) {
                    updateStatus(`错误: ${error.message}`);
                    console.error('发生错误', error);
                }

                async function loadModel() {
                    if (!avatarViewRef.value) {
                        updateStatus('错误: AvatarView组件未准备好');
                        return;
                    }
                    
                    try {
                        updateStatus('开始加载模型...');
                        // 这里使用一个示例模型URL，实际使用时请替换为真实的模型文件
                        const modelUrl = 'https://threejs.org/examples/models/gltf/Soldier/Soldier.glb';
                        const model = await avatarViewRef.value.loadModel(modelUrl);
                        updateStatus('模型加载成功');
                    } catch (error) {
                        updateStatus(`模型加载失败: ${error.message}`);
                    }
                }

                async function playAnimation() {
                    if (!avatarViewRef.value) {
                        updateStatus('错误: AvatarView组件未准备好');
                        return;
                    }
                    
                    try {
                        updateStatus('开始播放动画...');
                        // 这里使用一个示例动画URL，实际使用时请替换为真实的动画文件
                        const animUrl = 'https://threejs.org/examples/models/fbx/Samba Dancing.fbx';
                        await avatarViewRef.value.playAnimation(animUrl);
                        updateStatus('动画播放成功');
                    } catch (error) {
                        updateStatus(`动画播放失败: ${error.message}`);
                    }
                }

                function toggleDebug() {
                    debug.value = !debug.value;
                    updateStatus(`调试模式: ${debug.value ? '开启' : '关闭'}`);
                }

                function toggleControls() {
                    showControls.value = !showControls.value;
                    updateStatus(`控制器: ${showControls.value ? '显示' : '隐藏'}`);
                }

                return {
                    avatarViewRef,
                    debug,
                    showControls,
                    status,
                    handleSDKReady,
                    handleModelLoaded,
                    handleAnimationPlayed,
                    handleError,
                    loadModel,
                    playAnimation,
                    toggleDebug,
                    toggleControls
                };
            }
        })
        .use(AvatarJsSDK)
        .mount('#app');
    </script>
</body>
</html>