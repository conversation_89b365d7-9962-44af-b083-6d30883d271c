#!/usr/bin/env node

/**
 * 发布前检查脚本
 * 确保库可以正常发布到npm
 */

const fs = require('fs');
const path = require('path');

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description}: ${filePath}`);
    return true;
  } else {
    console.log(`❌ ${description}: ${filePath} (文件不存在)`);
    return false;
  }
}

function checkPackageJson() {
  const packagePath = path.join(__dirname, 'package.json');
  if (!fs.existsSync(packagePath)) {
    console.log('❌ package.json 不存在');
    return false;
  }
  
  const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  
  console.log('\n📦 Package.json 检查:');
  console.log(`   名称: ${pkg.name}`);
  console.log(`   版本: ${pkg.version}`);
  console.log(`   描述: ${pkg.description}`);
  console.log(`   主入口: ${pkg.main}`);
  console.log(`   模块入口: ${pkg.module}`);
  console.log(`   类型定义: ${pkg.types}`);
  
  const requiredFields = ['name', 'version', 'description', 'main', 'module', 'types'];
  const missingFields = requiredFields.filter(field => !pkg[field]);
  
  if (missingFields.length > 0) {
    console.log(`❌ 缺少必要字段: ${missingFields.join(', ')}`);
    return false;
  }
  
  console.log('✅ package.json 检查通过');
  return true;
}

function main() {
  console.log('🔍 Avatar JS SDK 发布前检查\n');
  
  let allChecksPass = true;
  
  // 检查必要文件
  console.log('📁 文件检查:');
  allChecksPass &= checkFile('package.json', 'Package配置文件');
  allChecksPass &= checkFile('README.md', '文档文件');
  allChecksPass &= checkFile('index.js', '主入口文件');
  allChecksPass &= checkFile('index.d.ts', 'TypeScript类型定义');
  allChecksPass &= checkFile('vite.config.js', '构建配置文件');
  
  // 检查构建产物
  console.log('\n🏗️  构建产物检查:');
  allChecksPass &= checkFile('dist/avatar-js-sdk.es.js', 'ES模块');
  allChecksPass &= checkFile('dist/avatar-js-sdk.cjs.js', 'CommonJS模块');
  allChecksPass &= checkFile('dist/avatar-js-sdk.umd.js', 'UMD模块');
  allChecksPass &= checkFile('dist/style.css', '样式文件');
  
  // 检查源码
  console.log('\n📝 源码检查:');
  allChecksPass &= checkFile('components/AvatarView.vue', 'Vue组件');
  allChecksPass &= checkFile('sdk/index.js', 'SDK入口');
  
  // 检查package.json
  allChecksPass &= checkPackageJson();
  
  console.log('\n' + '='.repeat(50));
  
  if (allChecksPass) {
    console.log('🎉 所有检查通过！库已准备好发布。');
    console.log('\n📋 发布步骤:');
    console.log('   1. npm run build  # 确保最新构建');
    console.log('   2. npm version patch|minor|major  # 更新版本号');
    console.log('   3. npm publish  # 发布到npm');
    process.exit(0);
  } else {
    console.log('❌ 检查失败！请修复上述问题后再发布。');
    process.exit(1);
  }
}

main();