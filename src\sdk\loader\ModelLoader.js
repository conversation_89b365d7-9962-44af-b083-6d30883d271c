import * as THREE from 'three';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { BaseLoader } from './BaseLoader';

/**
 * 模型加载器，支持FBX和GLB/GLTF格式
 */
export class ModelLoader extends BaseLoader {

    constructor() {
        super();
        this.fbxLoader = new FBXLoader();
        this.gltfLoader = new GLTFLoader();
    }

    /**
     * 根据文件扩展名判断使用哪个加载器
     * @param {string} path - 模型路径
     * @returns {string} - 文件格式类型
     */
    getFileFormat(path) {
        const extension = path.toLowerCase().split('.').pop();
        switch (extension) {
            case 'fbx':
                return 'fbx';
            case 'glb':
            case 'gltf':
                return 'gltf';
            default:
                throw new Error(`Unsupported file format: ${extension}`);
        }
    }

    async load(path, options = {}) {
        const mergedOptions = {
            scale: 100,
            position: new THREE.Vector3(0, -75, 0),
            rotation: new THREE.Euler(0, 0, 0),
            ...this.options,
            ...options
        };

        try {
            const format = this.getFileFormat(path);

            if (format === 'fbx') {
                return this.loadFBX(path, mergedOptions);
            } else if (format === 'gltf') {
                return this.loadGLTF(path, mergedOptions);
            }
        } catch (error) {
            this.emit('loadError', { url: path, error });
            console.log("load model ", path, " ", error);
            throw error;
        }
    }

    /**
     * 加载FBX模型
     * @param {string} path - 模型路径
     * @param {Object} options - 加载选项
     * @returns {Promise<THREE.Object3D>}
     */
    loadFBX(path, options) {
        return new Promise((resolve, reject) => {
            this.fbxLoader.load(
                path,
                (object) => {
                    this.processModel(object, options);
                    resolve(object);
                },
                undefined,
                (error) => {
                    console.log("load FBX model fail ", path, " ", error);
                    reject(error);
                }
            );
        });
    }

    /**
     * 加载GLTF/GLB模型
     * @param {string} path - 模型路径
     * @param {Object} options - 加载选项
     * @returns {Promise<THREE.Object3D>}
     */
    loadGLTF(path, options) {
        return new Promise((resolve, reject) => {
            this.gltfLoader.load(
                path,
                (gltf) => {
                    // GLTF加载器返回的是gltf对象，场景在gltf.scene中
                    const object = gltf.scene;
                    this.processModel(object, options);
                    resolve(object);
                },
                undefined,
                (error) => {
                    console.log("load GLTF/GLB model fail ", path, " ", error);
                    reject(error);
                }
            );
        });
    }


    /**
       * @param {THREE.Object3D} model
       * @param {Object} options
       * @private
       */
    processModel(model, options) {
        if (options.scale) {
            if (typeof options.scale === 'number') {
                model.scale.set(options.scale, options.scale, options.scale);
            } else if (options.scale instanceof THREE.Vector3) {
                model.scale.copy(options.scale);
            }
        }

        if (options.position) {
            model.position.copy(options.position);
        }

        if (options.rotation) {
            model.rotation.copy(options.rotation);
        }
        return model;
    }

}