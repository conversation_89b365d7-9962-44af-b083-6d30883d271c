<script setup>
import ClothAnimationDemo from './components/ClothAnimationDemo.vue';

</script>

<template>
  <div id="app">
    <div class="app-header">
      <h2 style="margin: 0; font-size: 1.25rem;">Cloth & Animation Demo</h2>
    </div>
    <ClothAnimationDemo class="viewer-container"></ClothAnimationDemo>
  </div>
</template>

<style scoped>
#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.app-header {
  background-color: #2c3e50;
  color: white;
  padding: 0.5rem;
  text-align: center;
}

.app-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}



.viewer-container {
  flex: 1;
  position: relative;
}
</style>
