import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { BaseLoader } from './BaseLoader';

/**
 * 模型加载器类
 * 负责加载3D模型资源
 */
export class ModelLoader extends BaseLoader {
  constructor() {
    super();
    this.gltfLoader = new GLTFLoader();
  }

  /**
   * 加载3D模型
   * @param {string} url - 模型URL
   * @param {Object} options - 加载选项
   * @returns {Promise<Object>} - 加载的模型对象
   * @override
   */
  async _loadResource(url, options = {}) {
    return new Promise((resolve, reject) => {
      const onProgress = options.onProgress || (() => {});
      
      this.gltfLoader.load(
        url,
        (gltf) => {
          const model = gltf.scene || gltf;
          
          // 应用默认设置
          if (options.castShadow) {
            model.traverse((child) => {
              if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
              }
            });
          }
          
          // 应用自定义处理函数
          if (typeof options.processModel === 'function') {
            options.processModel(model);
          }
          
          resolve(model);
        },
        onProgress,
        reject
      );
    });
  }
}