import { ModelLoader } from "../loader/ModelLoader";
import { EventEmitter } from "./EventEmitter";
import { SceneManager } from "./SceneManager";
import { AnimationLoader } from "../loader/AnimationLoader";

/**
 * Avatar JS SDK 核心类
 * 负责管理场景、模型加载和动画播放
 */
export default class AvatarSDK extends EventEmitter {

    /**
     * 创建AvatarSDK实例
     * @param {Object} options - 配置选项
     */
    constructor(options = {}) {
        super();
        this.options = {
            debug: false,
            showControls: false,
            ...options
        }
        this.model = null;
        this.initialized = false;
        this.modelLoader = new ModelLoader();
        this.animationLoader = new AnimationLoader();
        this.sceneManager = null;
    }

    /**
     * 初始化SDK
     * @param {HTMLCanvasElement} canvas - 用于渲染的canvas元素
     * @returns {Promise<void>}
     */
    async init(canvas) {
        if (this.initialized) {
            console.log("AvatarSDK already initialized");
            return;
        }

        try {
            this.sceneManager = new SceneManager(canvas,
                {
                    debug: this.options.debug,
                    showControls: this.options.showControls
                }
            );
            this.sceneManager.start();
            this.initialized = true;
            this.emit('initialized', { sdk: this });

        } catch (error) {
            this.emit('error', { error });
            throw error;
        }
    }

    /**
     * 加载3D模型
     * @param {string} url - 模型URL
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} - 加载的模型对象
     */
    async loadModel(url, options = {}) {
        try {
            this.model = await this.modelLoader.load(url, options);
            this.sceneManager.add(this.model);
            console.log("load model success", this.model.position);
            this.emit('modelLoaded', { model: this.model });
            return this.model;
        } catch (error) {
            console.log("load model error", error);
            this.emit('error', { error, type: 'modelLoad' });
            throw error;
        }
    }

    /**
     * 播放动画
     * @param {string} url - 动画URL
     * @param {Object} options - 播放选项
     * @returns {Promise<void>}
     */
    async playAnimation(url, options = {}) {
        try {
            const animObj = await this.animationLoader.load(url, options);
            console.log("animObj:", animObj);
            if(this.model == null){
                console.log("模型为空,请先加载模型");
                this.emit('error', { error: new Error("模型为空,请先加载模型"), type: 'animation' });
                return;
            }
            this.sceneManager.addAndPlayAnimation(animObj, this.model);
            this.emit('animationPlayed', { animation: animObj });
        } catch (error) {
            console.log("load anim error", error);
            this.emit('error', { error, type: 'animationLoad' });
            throw error;
        }
    }

    /**
     * 销毁SDK实例，释放资源
     */
    dispose() {
        if (this.sceneManager) {
            this.sceneManager.dispose();
        }
        this.model = null;
        this.initialized = false;
        this.emit('disposed');
    }
}