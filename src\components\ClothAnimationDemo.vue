<template>
    <div class="demo-container">
        <div class="controls">
            <h3>Cloth & Animation Demo</h3>
            <div class="status">{{ status }}</div>
            <div class="buttons">
                <button @click="loadBaseModel" :disabled="loading">Load Base Model</button>
                <button @click="loadCloth" :disabled="loading || !baseModelLoaded">Load Cloth</button>
                <button @click="playIdleAnimation" :disabled="loading || !baseModelLoaded">Play Idle Animation</button>
                <button @click="playCoolAnimation" :disabled="loading || !baseModelLoaded">Play Cool Animation</button>
                <button @click="resetScene" :disabled="loading">Reset Scene</button>
            </div>
            <div class="info">
                <p>Base Model: {{ baseModelLoaded ? '✅ Loaded' : '❌ Not loaded' }}</p>
                <p>Cloth: {{ clothLoaded ? '✅ Loaded' : '❌ Not loaded' }}</p>
                <p>Animation: {{ animationPlaying ? '🎬 Playing' : '⏸️ Stopped' }}</p>
            </div>
        </div>
        <canvas ref="demoCanvas" class="demo-canvas"></canvas>
    </div>
</template>

<script setup>
import { AvatarSDK, createAvatarSDK } from '@/sdk';
import { ref, onMounted, onUnmounted } from 'vue'

const demoCanvas = ref(null);
const status = ref('Initializing...');
const loading = ref(false);
const baseModelLoaded = ref(false);
const clothLoaded = ref(false);
const animationPlaying = ref(false);

/** @type {AvatarSDK} */
let avatarSDK

let model;

onMounted(async () => {
    try {
        avatarSDK = createAvatarSDK({debug: true, showControls: true});
        await avatarSDK.init(demoCanvas.value);
        status.value = 'SDK initialized. Ready to load models.';
    } catch (error) {
        console.error("init avatar error....", error);
        status.value = `Init error: ${error.message}`;
    }
})

const loadBaseModel = async () => {
    if (!avatarSDK || loading.value) return;
    
    try {
        loading.value = true;
        status.value = 'Loading base model...';
        
        model = await avatarSDK.loadModel("models/afdbda001.glb");
        console.log("load model ",model);
        
        baseModelLoaded.value = true;
        status.value = 'Base model loaded successfully!';
    } catch (error) {
        console.error('Base model loading error:', error);
        status.value = `Base model loading error: ${error.message}`;
    } finally {
        loading.value = false;
    }
}

const loadCloth = async () => {
    if (!avatarSDK || loading.value || !baseModelLoaded.value) return;
    
    try {
        loading.value = true;
        status.value = 'Loading cloth...';
        
        await avatarSDK.loadCloth("AFdWbA001_wb",model);
        
        clothLoaded.value = true;
        status.value = 'Cloth loaded successfully!';
    } catch (error) {
        console.error('Cloth loading error:', error);
        status.value = `Cloth loading error: ${error.message}`;
    } finally {
        loading.value = false;
    }
}

const playIdleAnimation = async () => {
    if (!avatarSDK || loading.value || !baseModelLoaded.value) return;
    
    try {
        loading.value = true;
        status.value = 'Playing idle animation...';
        
        await avatarSDK.playIdleAnimation();
        
        animationPlaying.value = true;
        status.value = 'Idle animation playing!';
    } catch (error) {
        console.error('Idle animation error:', error);
        status.value = `Idle animation error: ${error.message}`;
    } finally {
        loading.value = false;
    }
}

const playCoolAnimation = async () => {
    if (!avatarSDK || loading.value || !baseModelLoaded.value) return;
    
    try {
        loading.value = true;
        status.value = 'Playing cool animation...';
        
        await avatarSDK.playAnimation("anim/AFdZAni_cool_002_fixed/afdzani_cool_002_fixed.glb");
        
        animationPlaying.value = true;
        status.value = 'Cool animation playing!';
    } catch (error) {
        console.error('Cool animation error:', error);
        status.value = `Cool animation error: ${error.message}`;
    } finally {
        loading.value = false;
    }
}

const resetScene = async () => {
    if (!avatarSDK || loading.value) return;
    
    try {
        loading.value = true;
        status.value = 'Resetting scene...';
        
        // 重新初始化场景
        location.reload(); // 简单的重置方法
        
    } catch (error) {
        console.error('Reset error:', error);
        status.value = `Reset error: ${error.message}`;
    } finally {
        loading.value = false;
    }
}

onUnmounted(() => {
    if (avatarSDK) {
        // 清理资源
    }
})

</script>

<style scoped>
.demo-container {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.controls {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 100;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 20px;
    border-radius: 8px;
    min-width: 300px;
    max-width: 400px;
}

.controls h3 {
    margin: 0 0 15px 0;
    color: #fff;
}

.status {
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 14px;
}

.buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.buttons button {
    padding: 10px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.buttons button:hover:not(:disabled) {
    background: #0056b3;
}

.buttons button:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

.info {
    font-size: 12px;
    line-height: 1.4;
}

.info p {
    margin: 5px 0;
}

.demo-canvas {
    width: 100%;
    height: 100%;
    display: block;
}
</style>
