# Avatar JS SDK

基于Three.js的3D头像展示SDK，提供简单易用的API和Vue组件，用于在Web应用中展示3D模型和动画。

## 特性

- 基于Three.js的3D渲染
- 支持glTF/GLB模型加载
- 支持FBX动画加载和播放
- 提供Vue 3组件封装
- 支持TypeScript
- 轻量级，易于集成

## 安装

```bash
npm install avatar-js-sdk
```

## 快速开始

### 1. 作为Vue插件使用（全局注册）

```js
import { createApp } from 'vue';
import App from './App.vue';

// 方式1：默认导入
import AvatarJsSDK from 'avatar-js-sdk';
// 导入样式
import 'avatar-js-sdk/style.css';

// 方式2：命名导入
// import { plugin as AvatarJsSDK } from 'avatar-js-sdk';

const app = createApp(App);
app.use(AvatarJsSDK);
app.mount('#app');
```

### 2. 按需导入组件（推荐）

```js
import { createApp } from 'vue';
import App from './App.vue';
import { AvatarView } from 'avatar-js-sdk';
// 导入样式
import 'avatar-js-sdk/style.css';

const app = createApp(App);
app.component('AvatarView', AvatarView);
app.mount('#app');
```

### 在组件中使用

```vue
<template>
  <div class="avatar-container">
    <AvatarView 
      ref="avatarViewRef"
      :debug="false" 
      :showControls="true"
      @sdk-ready="handleSDKReady"
      @model-loaded="handleModelLoaded"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';

// 如果使用按需导入方式，需要导入组件
// import { AvatarView } from 'avatar-js-sdk';

const avatarViewRef = ref(null);

function handleSDKReady(sdk) {
  console.log('SDK准备就绪', sdk);
}

function handleModelLoaded(model) {
  console.log('模型加载完成', model);
}

// 加载模型
async function loadModel() {
  if (!avatarViewRef.value) return;
  
  try {
    const model = await avatarViewRef.value.loadModel('path/to/model.glb');
    console.log('加载模型成功', model);
  } catch (error) {
    console.error('加载模型失败', error);
  }
}

// 播放动画
async function playAnimation() {
  if (!avatarViewRef.value) return;
  
  try {
    await avatarViewRef.value.playAnimation('path/to/animation.fbx');
    console.log('播放动画成功');
  } catch (error) {
    console.error('播放动画失败', error);
  }
}
</script>
```

### 3. 直接使用SDK API

```js
import { createAvatarSDK } from 'avatar-js-sdk';

// 创建SDK实例
const sdk = createAvatarSDK({ debug: true, showControls: true });

// 初始化SDK
const canvas = document.getElementById('avatar-canvas');
await sdk.init(canvas);

// 加载模型
const model = await sdk.loadModel('path/to/model.glb');

// 播放动画
await sdk.playAnimation('path/to/animation.fbx');

// 监听事件
sdk.on('model-loaded', (model) => {
  console.log('模型加载完成', model);
});

// 销毁实例
sdk.dispose();
```

### 4. 在HTML中直接使用（UMD）

```html
<!DOCTYPE html>
<html>
<head>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/avatar-js-sdk/dist/avatar-js-sdk.umd.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/avatar-js-sdk/dist/style.css">
</head>
<body>
    <div id="app">
        <avatar-view :debug="true" @sdk-ready="handleReady"></avatar-view>
    </div>
    
    <script>
        const { createApp } = Vue;
        const { plugin: AvatarJsSDK } = window.AvatarJsSDK;
        
        createApp({
            methods: {
                handleReady(sdk) {
                    console.log('SDK准备就绪', sdk);
                }
            }
        })
        .use(AvatarJsSDK)
        .mount('#app');
    </script>
</body>
</html>
```

## API文档

### AvatarView 组件

#### Props

| 属性名 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| debug | Boolean | false | 是否开启调试模式 |
| showControls | Boolean | true | 是否显示轨道控制器 |
| modelUrl | String | '' | 模型URL，设置后会自动加载模型 |
| animationUrl | String | '' | 动画URL，设置后会自动播放动画 |

#### 事件

| 事件名 | 参数 | 描述 |
| --- | --- | --- |
| sdk-ready | sdk | SDK初始化完成事件 |
| model-loaded | model | 模型加载完成事件 |
| animation-played | - | 动画播放事件 |
| error | error | 错误事件 |

#### 方法

| 方法名 | 参数 | 返回值 | 描述 |
| --- | --- | --- | --- |
| getSDK | - | AvatarSDK | 获取SDK实例 |
| loadModel | url, options | Promise<Object3D> | 加载3D模型 |
| playAnimation | url, options | Promise<void> | 播放动画 |

### SDK API

#### createAvatarSDK(options)

创建AvatarSDK实例的工厂函数

```js
import { createAvatarSDK } from 'avatar-js-sdk';

const sdk = createAvatarSDK({
  debug: false,
  showControls: true
});
```

#### AvatarSDK 类

| 方法 | 参数 | 返回值 | 描述 |
| --- | --- | --- | --- |
| init | canvas | Promise<void> | 初始化SDK |
| loadModel | url, options | Promise<Object3D> | 加载3D模型 |
| playAnimation | url, options | Promise<void> | 播放动画 |
| dispose | - | void | 释放资源 |

## 许可证

MIT