<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GLB Loader Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: Arial, sans-serif;
        }
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            z-index: 100;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="info">
            <h3>GLB Loader Test</h3>
            <p id="status">Initializing...</p>
        </div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
        import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

        const statusEl = document.getElementById('status');
        
        function updateStatus(message) {
            statusEl.textContent = message;
            console.log(message);
        }

        // 创建场景
        const scene = new THREE.Scene();
        scene.background = new THREE.Color(0x222222);

        // 创建相机
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 0, 100);

        // 创建渲染器
        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.getElementById('container').appendChild(renderer.domElement);

        // 添加控制器
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;

        // 添加光源
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 50, 50);
        directionalLight.castShadow = true;
        scene.add(directionalLight);

        // 创建GLTFLoader
        const loader = new GLTFLoader();

        updateStatus('Loading GLB model...');

        // 加载GLB模型
        loader.load(
            '/models/afdbda001.glb',
            function (gltf) {
                updateStatus('GLB model loaded successfully!');
                
                const model = gltf.scene;
                
                // 设置模型位置
                model.position.set(0, -75, 0);
                
                // 启用阴影
                model.traverse((child) => {
                    if (child.isMesh) {
                        child.castShadow = true;
                        child.receiveShadow = true;
                    }
                });
                
                scene.add(model);
                
                // 计算模型边界框以调整相机位置
                const box = new THREE.Box3().setFromObject(model);
                const size = box.getSize(new THREE.Vector3()).length();
                const center = box.getCenter(new THREE.Vector3());
                
                camera.position.copy(center);
                camera.position.x += size / 2.0;
                camera.position.y += size / 5.0;
                camera.position.z += size / 2.0;
                camera.lookAt(center);
                
                updateStatus(`Model loaded! Size: ${size.toFixed(2)}, Center: (${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)})`);
            },
            function (progress) {
                const percent = (progress.loaded / progress.total * 100).toFixed(2);
                updateStatus(`Loading... ${percent}%`);
            },
            function (error) {
                updateStatus(`Error loading GLB model: ${error.message}`);
                console.error('GLB loading error:', error);
            }
        );

        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            renderer.render(scene, camera);
        }

        // 处理窗口大小变化
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        animate();
    </script>
</body>
</html>
