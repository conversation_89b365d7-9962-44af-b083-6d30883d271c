import { fileURLToPath, URL } from 'node:url'
import { resolve } from 'path';
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import { visualizer } from 'rollup-plugin-visualizer';
import copy from 'rollup-plugin-copy';
import staticFiles from 'rollup-plugin-static-files';

// 根据命令行参数确定是构建库还是运行demo
const isLib = process.env.BUILD_LIB === 'true';

// 库的版本号，从package.json中读取
import packageJson from './package.json';
const version = packageJson.version;

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
    visualizer({
      open: false,       // 不自动打开浏览器
      filename: './dist/stats.html'
    }),
    copy({
      targets: [
        { src: 'public/models', dest: 'dist' },
        { src: 'public/anim', dest: 'dist' },
      ],
    }),
    staticFiles({
      include: ['public'],
      publicPath: './'
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@lib': fileURLToPath(new URL('./lib', import.meta.url)),
      '@demo': fileURLToPath(new URL('./demo', import.meta.url))
    },
  },
  build: {
    outDir: isLib ? 'dist/lib' : 'dist',
    lib: isLib ? {
      entry: resolve(__dirname, 'lib/index.js'),
      name: 'AvatarJsSDK',
      fileName: (format) => `avatar-js-sdk.${format}.js`,
      formats: ['es', 'umd', 'cjs']
    } : undefined,
    rollupOptions: isLib ? {
      external: ['vue', 'three'],
      output: {
        // 为每种格式添加banner
        banner: `/**\n * Avatar JS SDK v${version}\n * (c) ${new Date().getFullYear()} Avatar JS SDK\n * Released under the MIT License.\n */`,
        globals: {
          vue: 'Vue',
          three: 'THREE'
        },
        // 使用命名导出，解决同时使用命名导出和默认导出的警告
        exports: 'named',
        // 确保CSS被提取到单独的文件
        assetFileNames: (assetInfo) => {
          if (assetInfo.name.endsWith('.css')) {
            return 'avatar-js-sdk.css';
          }
          return assetInfo.name;
        }
      }
    } : undefined,
    // 确保源码映射文件被生成
    sourcemap: true
  }
})
