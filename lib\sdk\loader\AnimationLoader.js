import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
import { BaseLoader } from './BaseLoader';

/**
 * 动画加载器类
 * 负责加载动画资源
 */
export class AnimationLoader extends BaseLoader {
  constructor() {
    super();
    this.fbxLoader = new FBXLoader();
  }

  /**
   * 加载动画资源
   * @param {string} url - 动画URL
   * @param {Object} options - 加载选项
   * @returns {Promise<Object>} - 加载的动画对象
   * @override
   */
  async _loadResource(url, options = {}) {
    return new Promise((resolve, reject) => {
      const onProgress = options.onProgress || (() => {});
      
      this.fbxLoader.load(
        url,
        (animation) => {
          // 应用自定义处理函数
          if (typeof options.processAnimation === 'function') {
            options.processAnimation(animation);
          }
          
          resolve(animation);
        },
        onProgress,
        reject
      );
    });
  }
}