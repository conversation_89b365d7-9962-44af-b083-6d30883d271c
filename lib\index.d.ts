// Avatar JS SDK 类型定义文件
import { App, Component } from 'vue';
import { Object3D, Scene, PerspectiveCamera, WebGLRenderer } from 'three';

// 事件发射器接口
export interface IEventEmitter {
  on(event: string, callback: Function): void;
  off(event: string, callback?: Function): void;
  emit(event: string, ...args: any[]): void;
}

// 场景管理器接口
export interface ISceneManager {
  scene: Scene;
  camera: PerspectiveCamera;
  renderer: WebGLRenderer;
  init(canvas: HTMLCanvasElement): void;
  start(): void;
  stop(): void;
  resize(): void;
  addObject(object: Object3D): void;
  removeObject(object: Object3D): void;
  dispose(): void;
}

// 加载器选项接口
export interface LoaderOptions {
  onProgress?: (progress: number) => void;
  castShadow?: boolean;
  receiveShadow?: boolean;
}

// 动画选项接口
export interface AnimationOptions {
  timeScale?: number;
  loop?: boolean;
  onProgress?: (progress: number) => void;
}

// SDK选项接口
export interface AvatarSDKOptions {
  debug?: boolean;
  showControls?: boolean;
}

// SDK接口
export interface IAvatarSDK extends IEventEmitter {
  init(canvas: HTMLCanvasElement): Promise<void>;
  loadModel(url: string, options?: LoaderOptions): Promise<Object3D | null>;
  playAnimation(url: string, options?: AnimationOptions): Promise<void>;
  dispose(): void;
}

// AvatarView组件Props接口
export interface AvatarViewProps {
  debug?: boolean;
  showControls?: boolean;
  modelUrl?: string;
  animationUrl?: string;
}

// AvatarView组件暴露的方法接口
export interface AvatarViewExpose {
  getSDK(): IAvatarSDK | null;
  loadModel(url: string, options?: LoaderOptions): Promise<Object3D | null>;
  playAnimation(url: string, options?: AnimationOptions): Promise<void>;
}

// AvatarView组件接口
export type AvatarViewComponent = Component & {
  props: AvatarViewProps;
  emits: ['sdk-ready', 'model-loaded', 'animation-played', 'error'];
  expose: AvatarViewExpose;
};

// Vue插件接口
export interface AvatarJsSDKPlugin {
  install(app: App, options?: any): void;
}

// 工厂函数
export function createAvatarSDK(options?: AvatarSDKOptions): IAvatarSDK;

// 类导出
export class AvatarSDK implements IAvatarSDK {
  constructor(options?: AvatarSDKOptions);
  init(canvas: HTMLCanvasElement): Promise<void>;
  loadModel(url: string, options?: LoaderOptions): Promise<Object3D | null>;
  playAnimation(url: string, options?: AnimationOptions): Promise<void>;
  dispose(): void;
  on(event: string, callback: Function): void;
  off(event: string, callback?: Function): void;
  emit(event: string, ...args: any[]): void;
}

export class SceneManager implements ISceneManager {
  constructor(canvas: HTMLCanvasElement, options?: any);
  scene: Scene;
  camera: PerspectiveCamera;
  renderer: WebGLRenderer;
  init(canvas: HTMLCanvasElement): void;
  start(): void;
  stop(): void;
  resize(): void;
  addObject(object: Object3D): void;
  removeObject(object: Object3D): void;
  dispose(): void;
}

export class EventEmitter implements IEventEmitter {
  on(event: string, callback: Function): void;
  off(event: string, callback?: Function): void;
  emit(event: string, ...args: any[]): void;
}

export class BaseLoader {
  constructor();
  load(url: string, options?: LoaderOptions): Promise<any>;
}

export class ModelLoader extends BaseLoader {
  constructor();
  load(url: string, options?: LoaderOptions): Promise<Object3D | null>;
}

export class AnimationLoader extends BaseLoader {
  constructor();
  load(url: string, options?: LoaderOptions): Promise<any>;
}

// 组件导出
export const AvatarView: AvatarViewComponent;
export const components: { AvatarView: AvatarViewComponent };

// 默认导出
declare const _default: AvatarJsSDKPlugin;
export default _default;