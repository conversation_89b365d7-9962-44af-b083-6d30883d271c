import * as THREE from 'three';
import { BaseLoader } from './BaseLoader';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

/**
 * 衣服加载器，处理cloth目录下的结构化衣服资源
 */
export class ClothLoader extends BaseLoader {
    constructor(options = {}) {
        super();
        this.options = {
            ...options
        }
        this.gltfLoader = new GLTFLoader();
    }

    /**
     * 加载衣服配置文件
     * @param {string} configPath - 配置文件路径
     * @returns {Promise<Object>} - 配置对象
     */
    async loadConfig(configPath) {
        try {
            const response = await fetch(configPath);
            if (!response.ok) {
                throw new Error(`Failed to load config: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error loading cloth config:', error);
            throw error;
        }
    }

    /**
     * 加载衣服模型
     * @param {string} clothName - 衣服名称（如：AFdWbA001_wb）
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} - 包含模型和配置的对象
     */
    async load(clothName, options = {}) {
        try {

            const mergedOptions = {
                        scale: 100,
                        position: new THREE.Vector3(0, -75, 0),
                        rotation: new THREE.Euler(0, 0, 0),
                        ...this.options,
                        ...options
                    };

            const basePath = `cloth/${clothName}`;
            
            // 加载配置文件
            const config = await this.loadConfig(`${basePath}/config.json`);
            console.log('Cloth config loaded:', config);
            
            // 加载GLB模型
            const modelPath = `${basePath}/${config.file}.glb`;
            const clothModel = await this.loadClothModel(modelPath, mergedOptions);

            // clothModel.traverse((node) =>{
            //     if (node.isSkinnedMesh) {
            //         node.bind(skeleton, node.matrixWorld);
            //     }
            // })
            
            return {
                model: clothModel,
                config: config,
                name: clothName
            };
        } catch (error) {
            console.error('Error loading cloth:', error);
            throw error;
        }
    }

    /**
     * 加载衣服GLB模型
     * @param {string} path - 模型路径
     * @param {Object} options - 加载选项
     * @returns {Promise<THREE.Object3D>}
     */
    async loadClothModel(path, options = {}) {
        return new Promise((resolve, reject) => {
            this.gltfLoader.load(
                path,
                (gltf) => {
                    const model = gltf.scene;
                    
                    // 应用变换选项
                    if (options.scale) {
                        if (typeof options.scale === 'number') {
                            console.log('Cloth model scale:', options.scale);
                            model.scale.set(options.scale, options.scale, options.scale);
                        }
                    }

                    model.scale.set(100, 100, 100);
                    model.position.set(0, -75, 0);

                    if (options.position) {
                        model.position.copy(options.position);
                    }

                    if (options.rotation) {
                        model.rotation.copy(options.rotation);
                    }

                    // 确保变换应用到所有子对象
                    model.updateMatrixWorld(true);

                    // 为了确保骨骼绑定时缩放不丢失，给每个SkinnedMesh添加标记
                    model.traverse((child) => {
                        if (child.isSkinnedMesh) {
                            child.userData.originalScale = model.scale.clone();
                            child.userData.originalPosition = model.position.clone();
                            child.userData.originalRotation = model.rotation.clone();
                            console.log(`标记SkinnedMesh变换: ${child.name}`, {
                                scale: child.userData.originalScale,
                                position: child.userData.originalPosition
                            });
                        }
                    });

                    console.log('Cloth model loaded with scale:', model.scale);
                    resolve(model);
                },
                undefined,
                (error) => {
                    console.error('Failed to load cloth model:', path, error);
                    reject(error);
                }
            );
        });
    }

    /**
     * 获取可用的衣服列表
     * @returns {Array<string>} - 衣服名称列表
     */
    getAvailableClothes() {
        // 这里可以扩展为动态获取cloth目录下的所有衣服
        return ['AFdWbA001_wb'];
    }
}
