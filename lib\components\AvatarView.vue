<template>
    <div class="avatar-viewer">
        <canvas ref="avatarViewCanvas" class="avatar-view-canvas"></canvas>
    </div>
</template>

<script setup>
import { AvatarSDK, createAvatarSDK } from '../sdk';
import { ref, onMounted, onUnmounted, defineComponent } from 'vue';

/**
 * AvatarView 组件
 * 
 * 3D头像展示组件，基于Three.js和AvatarSDK
 * 提供模型加载、动画播放等功能
 * 
 * @displayName AvatarView
 * @example
 * <template>
 *   <AvatarView 
 *     ref="avatarRef"
 *     :debug="false"
 *     :showControls="true"
 *     @sdk-ready="handleSDKReady"
 *   />
 * </template>
 */
defineComponent({
  name: 'AvatarView'
});

const avatarViewCanvas = ref(null);

/**
 * 组件属性定义
 */
const props = defineProps({
    /**
     * 是否开启调试模式
     * 开启后会显示性能监控面板
     */
    debug: {
        type: Boolean,
        default: false
    },
    /**
     * 是否显示轨道控制器
     * 开启后可以通过鼠标旋转、缩放场景
     */
    showControls: {
        type: Boolean,
        default: true
    },
    /**
     * 模型URL
     * 支持glTF/GLB格式，设置后会自动加载模型
     */
    modelUrl: {
        type: String,
        default: ''
    },
    /**
     * 动画URL
     * 支持FBX格式，设置后会自动播放动画
     * 注意：只有在modelUrl也设置的情况下才会生效
     */
    animationUrl: {
        type: String,
        default: ''
    }
});

/** @type {AvatarSDK} */
let avatarSDK;

/**
 * 定义组件事件
 * @property {AvatarSDK} sdk-ready - SDK初始化完成事件，参数为SDK实例
 * @property {THREE.Object3D} model-loaded - 模型加载完成事件，参数为加载的模型对象
 * @property {void} animation-played - 动画播放事件
 * @property {Error} error - 错误事件，参数为错误对象
 */
const emit = defineEmits(['sdk-ready', 'model-loaded', 'animation-played', 'error']);

onMounted(async () => {
    try {
        // 创建SDK实例
        avatarSDK = createAvatarSDK({debug: props.debug, showControls: props.showControls});
        
        // 初始化SDK
        await avatarSDK.init(avatarViewCanvas.value);
        emit('sdk-ready', avatarSDK);
        
        // 如果提供了模型URL，自动加载模型
        if (props.modelUrl) {
            const model = await avatarSDK.loadModel(props.modelUrl);
            emit('model-loaded', model);
            
            // 如果提供了动画URL，自动播放动画
            if (props.animationUrl) {
                await avatarSDK.playAnimation(props.animationUrl);
                emit('animation-played');
            }
        }
    } catch (error) {
        console.error("初始化Avatar出错:", error);
        emit('error', error);
    }
});

onUnmounted(() => {
    // 清理资源
    if (avatarSDK && avatarSDK.sceneManager) {
        avatarSDK.sceneManager.dispose();
    }
});

/**
 * 暴露方法给父组件
 * 这些方法可以通过ref引用访问
 */
defineExpose({
    /**
     * 获取SDK实例
     * @returns {AvatarSDK|null} SDK实例，如果未初始化则返回null
     */
    getSDK() {
        return avatarSDK;
    },
    
    /**
     * 加载3D模型
     * @param {string} url - 模型URL，支持glTF/GLB格式
     * @param {Object} [options] - 加载选项
     * @param {boolean} [options.castShadow=true] - 是否产生阴影
     * @param {boolean} [options.receiveShadow=true] - 是否接收阴影
     * @param {Function} [options.onProgress] - 加载进度回调
     * @returns {Promise<THREE.Object3D|null>} 加载的模型对象
     */
    async loadModel(url, options) {
        if (!avatarSDK) return null;
        return await avatarSDK.loadModel(url, options);
    },
    
    /**
     * 播放动画
     * @param {string} url - 动画URL，支持FBX格式
     * @param {Object} [options] - 播放选项
     * @param {number} [options.timeScale=1] - 动画播放速度
     * @param {boolean} [options.loop=true] - 是否循环播放
     * @param {Function} [options.onProgress] - 加载进度回调
     * @returns {Promise<void>}
     */
    async playAnimation(url, options) {
        if (!avatarSDK) return;
        return await avatarSDK.playAnimation(url, options);
    }
});
</script>

<style scoped>
.avatar-viewer {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.avatar-view-canvas {
    width: 100%;
    height: 100%;
    display: block;
}
</style>