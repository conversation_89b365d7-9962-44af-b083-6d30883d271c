import { createApp } from 'vue';
import App from './App.vue';

// 方式1：导入整个SDK作为插件（全局注册组件）
// import AvatarJsSDK from '../lib'; // 或者使用命名导入: import { plugin as AvatarJsSDK } from '../lib';

// 方式2：按需导入组件（推荐，按需加载）
import { AvatarView } from '../lib';
// 导入样式
import '../lib/dist/style.css';

// 创建Vue应用
const app = createApp(App);

// 方式1：注册Avatar JS SDK插件（全局注册）
// app.use(AvatarJsSDK);

// 方式2：手动注册需要的组件（按需注册）
app.component('AvatarView', AvatarView);

// 挂载应用
app.mount('#app');

console.log('Avatar JS SDK Demo 应用已启动 - 使用独立库按需导入方式');