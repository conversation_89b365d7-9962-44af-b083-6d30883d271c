// Avatar JS SDK 核心入口文件
import AvatarSDK from "./core/AvatarSDK";
import { BaseLoader } from "./loader/BaseLoader";
import { FBXLoader } from "three/examples/jsm/loaders/FBXLoader";
import { SceneManager } from "./core/SceneManager";

/**
 * 创建AvatarSDK实例的工厂函数
 * @param {Object} options - SDK配置选项
 * @returns {AvatarSDK} 返回SDK实例
 */
export function createAvatarSDK(options = {}) {
    const sdk = new AvatarSDK(options);
    return sdk;
}

// 导出SDK核心类和工具
export {
    AvatarSDK,
    SceneManager,
    BaseLoader,
    FBXLoader,
};