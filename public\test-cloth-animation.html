<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloth and Animation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cloth and Animation Test</h1>
        <p>This page tests cloth loading and idle animation functionality.</p>
        
        <div class="status" id="status">Ready to test...</div>
        
        <button onclick="testClothConfig()">Test Cloth Config Loading</button>
        <button onclick="testClothModel()">Test Cloth Model Loading</button>
        <button onclick="testIdleAnimation()">Test Idle Animation</button>
        <button onclick="clearLog()">Clear Log</button>
        
        <div id="log" class="log">Click a button to start testing...</div>
    </div>

    <script type="module">
        import * as THREE from 'three';
        import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('status');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(message) {
            statusElement.textContent = message;
            log(`STATUS: ${message}`);
        }

        window.clearLog = function() {
            logElement.textContent = '';
        }

        // 测试衣服配置加载
        window.testClothConfig = async function() {
            updateStatus('Testing cloth config loading...');
            
            try {
                const response = await fetch('/cloth/AFdWbA001_wb/config.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const config = await response.json();
                log('✅ Cloth config loaded successfully!');
                log(`Config details:`);
                log(`  - Name: ${config.res[0].name}`);
                log(`  - Sex: ${config.sex}`);
                log(`  - File: ${config.file}`);
                log(`  - Mesh parts: ${config.res[0].mesh.join(', ')}`);
                log(`  - Mask: [${config.res[0].mask.join(', ')}]`);
                
                updateStatus('Cloth config test passed!');
            } catch (error) {
                log(`❌ Cloth config test failed: ${error.message}`);
                updateStatus('Cloth config test failed!');
            }
        }

        // 测试衣服模型加载
        window.testClothModel = async function() {
            updateStatus('Testing cloth model loading...');
            
            try {
                const loader = new GLTFLoader();
                
                loader.load(
                    '/cloth/AFdWbA001_wb/afdwba001_wb.glb',
                    function(gltf) {
                        log('✅ Cloth model loaded successfully!');
                        log(`Model details:`);
                        log(`  - Scene type: ${gltf.scene.type}`);
                        log(`  - Children count: ${gltf.scene.children.length}`);
                        
                        // 遍历模型的子对象
                        gltf.scene.traverse((child) => {
                            if (child.isMesh) {
                                log(`  - Found mesh: ${child.name || 'unnamed'}`);
                            }
                        });
                        
                        updateStatus('Cloth model test passed!');
                    },
                    function(progress) {
                        const percent = (progress.loaded / progress.total * 100).toFixed(2);
                        log(`Loading cloth model: ${percent}%`);
                    },
                    function(error) {
                        log(`❌ Cloth model test failed: ${error.message}`);
                        updateStatus('Cloth model test failed!');
                    }
                );
            } catch (error) {
                log(`❌ Cloth model test failed: ${error.message}`);
                updateStatus('Cloth model test failed!');
            }
        }

        // 测试idle动画加载
        window.testIdleAnimation = async function() {
            updateStatus('Testing idle animation loading...');
            
            try {
                const loader = new GLTFLoader();
                
                loader.load(
                    '/anim/AFdZAni_idle_000_v06_fixed/afdzani_idle_000_v06_fixed.glb',
                    function(gltf) {
                        log('✅ Idle animation loaded successfully!');
                        log(`Animation details:`);
                        log(`  - Scene type: ${gltf.scene.type}`);
                        log(`  - Animations count: ${gltf.animations.length}`);
                        
                        if (gltf.animations.length > 0) {
                            const anim = gltf.animations[0];
                            log(`  - Animation name: ${anim.name}`);
                            log(`  - Duration: ${anim.duration.toFixed(2)}s`);
                            log(`  - Tracks count: ${anim.tracks.length}`);
                        }
                        
                        updateStatus('Idle animation test passed!');
                    },
                    function(progress) {
                        const percent = (progress.loaded / progress.total * 100).toFixed(2);
                        log(`Loading idle animation: ${percent}%`);
                    },
                    function(error) {
                        log(`❌ Idle animation test failed: ${error.message}`);
                        updateStatus('Idle animation test failed!');
                    }
                );
            } catch (error) {
                log(`❌ Idle animation test failed: ${error.message}`);
                updateStatus('Idle animation test failed!');
            }
        }

        log('Cloth and Animation Test page loaded. Ready for testing.');
    </script>
</body>
</html>
