import * as THREE from 'three';
import { ModelLoader } from "../loader/ModelLoader";
import { EventEmitter } from "./EventEmitter";
import { SceneManager } from "./SceneManager";
import { AnimationLoader } from "../loader/AnimationLoader";
import { <PERSON><PERSON>hLoader } from "../loader/ClothLoader";
import { SkeletonBinder } from "../utils/SkeletonBinder";


export default class AvatarSDK extends EventEmitter {

    constructor(options = {}) {
        super();
        this.options = {
            debug: false,
            showControls: false,
            ...options
        }
        this.model = null;
        this.clothes = []; // 存储已加载的衣服
        this.initialized = false;
        this.modelLoader = new ModelLoader();
        this.animationLoader = new AnimationLoader();
        this.clothLoader = new ClothLoader();
        this.skeletonBinder = new SkeletonBinder(); // 骨骼绑定器
        this.sceneManager = null;
    }

    async init(canvas) {
        if (this.initialized) {
            console.log("AvatarSDK already initialized");
            return;
        }

        try {
            this.sceneManager = new SceneManager(canvas,
                {
                    debug: this.options.debug,
                    showControls: this.options.showControls
                }
            );
            this.sceneManager.start();
            this.initialized = true;

        } catch (error) {

        }
    }

    async loadModel(url, options = {}) {
        try {
            this.model = await this.modelLoader.load(url, options);
            this.sceneManager.add(this.model);
            console.log("load model success", this.model.position);
            return this.model;
        } catch (error) {
            console.log("load model error", error);
        }
    }

    async playAnimation(url, options = {}) {
        try {
            const animObj = await this.animationLoader.load(url, options);
            console.log("animObj:", animObj);
            if (this.model == null) {
                console.log("模型为空,请先加载模型");
                return;
            }
            this.sceneManager.addAndPlayAnimation(animObj, this.model);
        } catch (error) {
            console.log("load anim error", error);
        }
    }

    /**
     * 加载衣服并绑定到角色骨骼
     * @param {string} clothName - 衣服名称
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} - 衣服对象
     */
    async loadCloth(clothName, options = {}) {
        try {
            if (!this.model) {
                throw new Error("请先加载角色模型再加载衣服");
            }

            console.log(`开始加载衣服: ${clothName}`);
            const clothObj = await this.clothLoader.load(clothName, options);
            console.log("衣服加载完成:", clothObj);

            //尝试绑定衣服骨骼到角色骨骼
            const bindSuccess = this.skeletonBinder.bindClothToCharacter(
                this.model,
                clothObj.model,
                options
            );

            if (bindSuccess) {
                console.log("✅ 衣服骨骼绑定成功");
            } else {
                console.warn("⚠️ 衣服骨骼绑定失败，衣服将独立显示");
            }

            // 将衣服添加到场景中
            this.sceneManager.add(clothObj.model);

            // 存储衣服引用
            this.clothes.push({
                ...clothObj,
                isBound: bindSuccess
            });

            return clothObj;
        } catch (error) {
            console.error("加载衣服失败:", error);
            throw error;
        }
    }

    /**
     * 播放idle动画
     * @param {Object} options - 播放选项
     * @returns {Promise<void>}
     */
    async playIdleAnimation(options = {}) {
        try {
            // 使用GLB格式的idle动画
            await this.playAnimation("anim/AFdZAni_idle_000_v06_fixed/afdzani_idle_000_v06_fixed.glb", options);
        } catch (error) {
            console.log("play idle animation error", error);
            throw error;
        }
    }


}