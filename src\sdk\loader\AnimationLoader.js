import { BaseLoader } from './BaseLoader';
import { CustomFBXLoader } from './CustomFBXLoader';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';

export class AnimationLoader extends BaseLoader {
    constructor(options = {}) {
        super();
        this.options = {
            ...options
        }
        this.fbxLoader = new CustomFBXLoader();
        this.gltfLoader = new GLTFLoader();
    }

    /**
     * 根据文件扩展名判断使用哪个加载器
     * @param {string} path - 动画路径
     * @returns {string} - 文件格式类型
     */
    getFileFormat(path) {
        const extension = path.toLowerCase().split('.').pop();
        switch (extension) {
            case 'fbx':
                return 'fbx';
            case 'glb':
            case 'gltf':
                return 'gltf';
            default:
                throw new Error(`Unsupported animation format: ${extension}`);
        }
    }

    async load(path, options = {}) {
        try {
            const format = this.getFileFormat(path);

            if (format === 'fbx') {
                return await this.fbxLoader.load(path, options);
            } else if (format === 'gltf') {
                return await this.loadGLTFAnimation(path, options);
            }
        } catch (error) {
            throw error;
        }
    }

    /**
     * 加载GLTF/GLB动画
     * @param {string} path - 动画路径
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>}
     */
    async loadGLTFAnimation(path, options = {}) {
        return new Promise((resolve, reject) => {
            this.gltfLoader.load(
                path,
                (gltf) => {
                    // GLTF动画数据在gltf.animations中
                    resolve(gltf);
                },
                undefined,
                (error) => {
                    console.log("load GLTF/GLB animation fail ", path, " ", error);
                    reject(error);
                }
            );
        });
    }
}