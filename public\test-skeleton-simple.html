<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Skeleton Binding Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #e9ecef;
        }
        .canvas-container {
            width: 100%;
            height: 400px;
            border: 2px solid #dee2e6;
            border-radius: 4px;
            margin: 20px 0;
            position: relative;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦴 Simple Skeleton Binding Test</h1>
        <p>This page tests the skeleton binding functionality using CDN Three.js.</p>
        
        <div class="status" id="status">Ready to test skeleton binding...</div>
        
        <div>
            <button id="load-character">Load Character Model</button>
            <button id="load-cloth" disabled>Load Cloth Model</button>
            <button id="test-binding" disabled>Test Skeleton Binding</button>
            <button id="play-anim" disabled>Play Animation</button>
            <button id="clear-log">Clear Log</button>
        </div>
        
        <div class="canvas-container">
            <canvas id="test-canvas" style="width: 100%; height: 100%;"></canvas>
        </div>
        
        <div id="log" class="log">Loading Three.js from CDN...</div>
    </div>

    <!-- 使用CDN加载Three.js -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>

    <script>
        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('status');
        
        let scene, camera, renderer, controls;
        let characterModel = null;
        let clothModel = null;
        let mixer = null;
        
        // 初始化3D场景
        function initScene() {
            const canvas = document.getElementById('test-canvas');
            
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x222222);
            
            camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
            camera.position.set(0, 0, 200);
            
            renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            renderer.shadowMap.enabled = true;
            
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            
            // 添加光源
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(50, 50, 50);
            scene.add(directionalLight);
            
            // 渲染循环
            function animate() {
                requestAnimationFrame(animate);
                controls.update();
                
                if (mixer) {
                    mixer.update(0.016); // 60fps
                }
                
                renderer.render(scene, camera);
            }
            animate();
            
            log('✅ 3D场景初始化完成');
        }
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(message) {
            statusElement.textContent = message;
            log(`STATUS: ${message}`);
        }

        function clearLog() {
            logElement.textContent = '';
        }

        // 加载角色模型
        async function loadCharacterModel() {
            updateStatus('Loading character model...');
            
            try {
                const loader = new THREE.GLTFLoader();
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load('/models/afdbda001.glb', resolve, undefined, reject);
                });
                
                characterModel = gltf.scene;
                characterModel.scale.set(100, 100, 100);
                characterModel.position.set(0, -75, 0);
                
                scene.add(characterModel);
                
                // 分析角色骨骼
                analyzeModel(characterModel, 'Character');
                
                document.getElementById('load-cloth').disabled = false;
                updateStatus('Character model loaded successfully!');
                
            } catch (error) {
                log(`❌ Character model loading failed: ${error.message}`);
                updateStatus('Character model loading failed!');
            }
        }

        // 加载衣服模型
        async function loadClothModel() {
            updateStatus('Loading cloth model...');
            
            try {
                const loader = new THREE.GLTFLoader();
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load('/cloth/AFdWbA001_wb/afdwba001_wb.glb', resolve, undefined, reject);
                });
                
                clothModel = gltf.scene;
                clothModel.scale.set(100, 100, 100);
                clothModel.position.set(0, -75, 0);
                
                scene.add(clothModel);
                
                // 分析衣服骨骼
                analyzeModel(clothModel, 'Cloth');
                
                document.getElementById('test-binding').disabled = false;
                updateStatus('Cloth model loaded successfully!');
                
            } catch (error) {
                log(`❌ Cloth model loading failed: ${error.message}`);
                updateStatus('Cloth model loading failed!');
            }
        }

        // 分析模型骨骼信息
        function analyzeModel(model, type) {
            let skinnedMeshCount = 0;
            let boneNames = [];
            
            model.traverse((node) => {
                if (node.isSkinnedMesh) {
                    skinnedMeshCount++;
                    if (node.skeleton) {
                        const bones = node.skeleton.bones.map(bone => bone.name);
                        boneNames = boneNames.concat(bones);
                        log(`${type} SkinnedMesh: ${node.name} (${bones.length} bones)`);
                    }
                }
            });
            
            log(`${type} 总计: ${skinnedMeshCount} SkinnedMesh, ${boneNames.length} bones`);
            if (boneNames.length > 0) {
                log(`${type} 骨骼示例: ${boneNames.slice(0, 5).join(', ')}${boneNames.length > 5 ? '...' : ''}`);
            }
        }

        // 测试骨骼绑定
        function testSkeletonBinding() {
            if (!characterModel || !clothModel) {
                log('❌ 请先加载角色和衣服模型');
                return;
            }
            
            updateStatus('Testing skeleton binding...');
            log('🔄 开始骨骼绑定测试...');
            
            try {
                // 获取角色骨骼
                let characterSkeleton = null;
                characterModel.traverse((node) => {
                    if (node.isSkinnedMesh && node.skeleton) {
                        characterSkeleton = node.skeleton;
                        log(`找到角色骨骼系统: ${node.name}`);
                    }
                });
                
                if (!characterSkeleton) {
                    log('❌ 角色模型中未找到骨骼系统');
                    return;
                }
                
                // 绑定衣服骨骼
                let bindCount = 0;
                clothModel.traverse((node) => {
                    if (node.isSkinnedMesh) {
                        try {
                            log(`🔄 正在绑定: ${node.name}`);
                            
                            // 简单的骨骼绑定
                            const clothBones = node.skeleton.bones;
                            const characterBones = characterSkeleton.bones;
                            
                            // 创建骨骼映射
                            const newBones = [];
                            let matchCount = 0;
                            
                            for (let clothBone of clothBones) {
                                const matchedBone = characterBones.find(cb => cb.name === clothBone.name);
                                if (matchedBone) {
                                    newBones.push(matchedBone);
                                    matchCount++;
                                } else {
                                    newBones.push(clothBone);
                                }
                            }
                            
                            // 创建新骨骼系统并绑定
                            const newSkeleton = new THREE.Skeleton(newBones);
                            node.bind(newSkeleton, node.matrixWorld);
                            
                            bindCount++;
                            log(`✅ 成功绑定: ${node.name} (匹配 ${matchCount}/${clothBones.length} 骨骼)`);
                            
                        } catch (error) {
                            log(`❌ 绑定失败 ${node.name}: ${error.message}`);
                        }
                    }
                });
                
                if (bindCount > 0) {
                    document.getElementById('play-anim').disabled = false;
                    updateStatus(`✅ 骨骼绑定完成! 成功绑定 ${bindCount} 个网格`);
                    log(`🎉 骨骼绑定测试完成，成功绑定 ${bindCount} 个SkinnedMesh`);
                } else {
                    updateStatus('❌ 骨骼绑定失败!');
                }
                
            } catch (error) {
                log(`❌ 骨骼绑定测试失败: ${error.message}`);
                updateStatus('❌ 骨骼绑定测试失败!');
            }
        }

        // 播放动画
        async function playAnimation() {
            if (!characterModel) {
                log('❌ 请先加载角色模型');
                return;
            }
            
            updateStatus('Loading and playing animation...');
            
            try {
                const loader = new THREE.GLTFLoader();
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load('/anim/AFdZAni_idle_000_v06_fixed/afdzani_idle_000_v06_fixed.glb', resolve, undefined, reject);
                });
                
                if (gltf.animations && gltf.animations.length > 0) {
                    mixer = new THREE.AnimationMixer(characterModel);
                    const action = mixer.clipAction(gltf.animations[0]);
                    action.setLoop(THREE.LoopRepeat);
                    action.play();
                    
                    log(`🎬 动画开始播放: ${gltf.animations[0].name}`);
                    log(`📊 动画信息: 时长 ${gltf.animations[0].duration.toFixed(2)}s`);
                    updateStatus('🎬 动画播放中! 观察角色和衣服是否同步运动');
                } else {
                    log('❌ 动画文件中未找到动画数据');
                }
                
            } catch (error) {
                log(`❌ 动画加载失败: ${error.message}`);
                updateStatus('❌ 动画加载失败!');
            }
        }

        // 绑定事件监听器
        function bindEventListeners() {
            document.getElementById('load-character').addEventListener('click', loadCharacterModel);
            document.getElementById('load-cloth').addEventListener('click', loadClothModel);
            document.getElementById('test-binding').addEventListener('click', testSkeletonBinding);
            document.getElementById('play-anim').addEventListener('click', playAnimation);
            document.getElementById('clear-log').addEventListener('click', clearLog);
        }

        // 等待Three.js加载完成后初始化
        window.addEventListener('load', function() {
            if (typeof THREE !== 'undefined') {
                log('✅ Three.js CDN 加载完成');
                initScene();
                bindEventListeners();
                log('🦴 Simple Skeleton Binding Test 准备就绪');
            } else {
                log('❌ Three.js 加载失败');
            }
        });
    </script>
</body>
</html>
