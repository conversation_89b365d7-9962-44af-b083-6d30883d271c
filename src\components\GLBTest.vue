<template>
    <div class="glb-test">
        <div class="controls">
            <h3>GLB Loader Test</h3>
            <button @click="loadFBXModel">Load FBX Model</button>
            <button @click="loadGLBModel">Load GLB Model</button>
            <p>Status: {{ status }}</p>
        </div>
        <canvas ref="testCanvas" class="test-canvas"></canvas>
    </div>
</template>

<script setup>
import { AvatarSDK, createAvatarSDK } from '@/sdk';
import { ref, onMounted, onUnmounted } from 'vue'

const testCanvas = ref(null);
const status = ref('Initializing...');

/** @type {AvatarSDK} */
let avatarSDK

onMounted(async () => {
    try {
        avatarSDK = createAvatarSDK({debug: true, showControls: true});
        await avatarSDK.init(testCanvas.value);
        status.value = 'SDK initialized successfully';
    } catch (error) {
        console.log("init avatar error....", error);
        status.value = `Init error: ${error.message}`;
    }
})

const loadFBXModel = async () => {
    if (!avatarSDK) {
        status.value = 'SDK not initialized';
        return;
    }
    
    try {
        status.value = 'Loading FBX model...';
        await avatarSDK.loadModel("models/AFdBdA001.fbx");
        status.value = 'FBX model loaded successfully!';
    } catch (error) {
        console.error('FBX loading error:', error);
        status.value = `FBX loading error: ${error.message}`;
    }
}

const loadGLBModel = async () => {
    if (!avatarSDK) {
        status.value = 'SDK not initialized';
        return;
    }
    
    try {
        status.value = 'Loading GLB model...';
        await avatarSDK.loadModel("models/afdbda001.glb");
        status.value = 'GLB model loaded successfully!';
    } catch (error) {
        console.error('GLB loading error:', error);
        status.value = `GLB loading error: ${error.message}`;
    }
}

onUnmounted(() => {
    if (avatarSDK) {
        // 清理资源
    }
})

</script>

<style scoped>
.glb-test {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.controls {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 100;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px;
    border-radius: 5px;
    min-width: 250px;
}

.controls button {
    margin: 5px;
    padding: 8px 16px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.controls button:hover {
    background: #0056b3;
}

.test-canvas {
    width: 100%;
    height: 100%;
    display: block;
}
</style>
