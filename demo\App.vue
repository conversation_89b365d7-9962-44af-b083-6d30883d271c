<template>
  <div class="demo-container">
    <header class="demo-header">
      <h1>Avatar JS SDK 演示</h1>
    </header>
    
    <main class="demo-content">
      <div class="avatar-container">
        <AvatarView 
          ref="avatarViewRef"
          :debug="debug" 
          :showControls="showControls"
          @sdk-ready="handleSDKReady"
          @model-loaded="handleModelLoaded"
          @animation-played="handleAnimationPlayed"
          @error="handleError"
        />
      </div>
      
      <div class="controls-panel">
        <div class="control-group">
          <h3>模型控制</h3>
          <button @click="loadModel">加载模型</button>
          <select v-model="selectedModel">
            <option value="models/Soldier.glb">士兵模型</option>
          </select>
        </div>
        
        <div class="control-group">
          <h3>动画控制</h3>
          <button @click="playAnimation" :disabled="!modelLoaded">播放动画</button>
          <select v-model="selectedAnimation">
            <option value="anim/Locking Hip Hop Dance.fbx">Hip Hop 舞蹈</option>
          </select>
        </div>
        
        <div class="control-group">
          <h3>设置</h3>
          <div class="checkbox-control">
            <input type="checkbox" id="debug" v-model="debug">
            <label for="debug">调试模式</label>
          </div>
          <div class="checkbox-control">
            <input type="checkbox" id="showControls" v-model="showControls">
            <label for="showControls">显示控制器</label>
          </div>
        </div>
      </div>
    </main>
    
    <footer class="demo-footer">
      <p>Avatar JS SDK - 基于Three.js的3D头像展示SDK</p>
    </footer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { AvatarView } from '../lib';

// 引用和状态
const avatarViewRef = ref(null);
const debug = ref(true);
const showControls = ref(true);
const modelLoaded = ref(false);
const selectedModel = ref('models/Soldier.glb');
const selectedAnimation = ref('anim/Locking Hip Hop Dance.fbx');

// SDK实例
let avatarSDK = null;

// 事件处理函数
function handleSDKReady(sdk) {
  console.log('SDK准备就绪', sdk);
  avatarSDK = sdk;
}

function handleModelLoaded(model) {
  console.log('模型加载完成', model);
  modelLoaded.value = true;
}

function handleAnimationPlayed() {
  console.log('动画播放中');
}

function handleError(error) {
  console.error('发生错误', error);
}

// 操作函数
async function loadModel() {
  if (!avatarViewRef.value) return;
  
  try {
    const model = await avatarViewRef.value.loadModel(selectedModel.value);
    console.log('加载模型成功', model);
    modelLoaded.value = true;
  } catch (error) {
    console.error('加载模型失败', error);
  }
}

async function playAnimation() {
  if (!avatarViewRef.value || !modelLoaded.value) return;
  
  try {
    await avatarViewRef.value.playAnimation(selectedAnimation.value);
    console.log('播放动画成功');
  } catch (error) {
    console.error('播放动画失败', error);
  }
}

// 生命周期
onMounted(() => {
  console.log('Demo页面已挂载');
});
</script>

<style scoped>
.demo-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-family: Arial, sans-serif;
}

.demo-header {
  background-color: #2c3e50;
  color: white;
  padding: 1rem;
  text-align: center;
}

.demo-content {
  display: flex;
  flex: 1;
  padding: 1rem;
}

.avatar-container {
  flex: 3;
  height: 70vh;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.controls-panel {
  flex: 1;
  margin-left: 1rem;
  padding: 1rem;
  background-color: #f0f0f0;
  border-radius: 8px;
}

.control-group {
  margin-bottom: 1.5rem;
}

h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: #2c3e50;
}

button {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
}

button:hover {
  background-color: #2980b9;
}

button:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
}

select {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #ddd;
  width: 100%;
  margin-bottom: 0.5rem;
}

.checkbox-control {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.checkbox-control label {
  margin-left: 0.5rem;
}

.demo-footer {
  background-color: #2c3e50;
  color: white;
  padding: 1rem;
  text-align: center;
}
</style>