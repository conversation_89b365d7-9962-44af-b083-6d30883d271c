/**
 * Avatar JS SDK 库入口文件
 * 
 * 这个文件导出SDK的所有核心类、工具和组件，可以通过命名导入或默认导入使用
 * 命名导入示例: import { AvatarView, createAvatarSDK } from 'avatar-js-sdk';
 * 默认导入示例: import AvatarJsSDK from 'avatar-js-sdk'; app.use(AvatarJsSDK);
 * 
 * @module avatar-js-sdk
 */

// 导入SDK核心类和工具
import { AvatarSDK, createAvatarSDK, SceneManager, BaseLoader } from './sdk';
import { ModelLoader } from './sdk/loader/ModelLoader';
import { AnimationLoader } from './sdk/loader/AnimationLoader';
import { EventEmitter } from './sdk/core/EventEmitter';

// 导入组件
import AvatarView from './components/AvatarView.vue';

// 导出SDK核心类和工具
export {
  // 核心类
  AvatarSDK,
  createAvatarSDK,
  SceneManager,
  EventEmitter,
  
  // 加载器
  BaseLoader,
  ModelLoader,
  AnimationLoader,
  
  // 组件
  AvatarView
};

// 导出组件集合
export const components = {
  AvatarView
};

/**
 * Vue插件安装方法
 * 提供全局注册组件和添加全局属性的功能
 * 
 * @example
 * // 全局注册
 * import { createApp } from 'vue';
 * import App from './App.vue';
 * import AvatarJsSDK from 'avatar-js-sdk';
 * 
 * const app = createApp(App);
 * app.use(AvatarJsSDK);
 * app.mount('#app');
 */
export const plugin = {
  install(app, options = {}) {
    // 注册全局组件
    app.component('AvatarView', AvatarView);
    
    // 添加全局属性
    app.config.globalProperties.$avatarSDK = {
      createSDK: createAvatarSDK,
      version: process.env.VERSION || '1.0.0'
    };
  }
};

// 默认导出Vue插件
export default plugin;