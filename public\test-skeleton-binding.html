<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skeleton Binding Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #e9ecef;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .info-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .info-box h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .canvas-container {
            width: 100%;
            height: 400px;
            border: 2px solid #dee2e6;
            border-radius: 4px;
            margin: 20px 0;
            position: relative;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🦴 Skeleton Binding Test</h1>
        <p>This page tests the skeleton binding functionality between character and cloth models.</p>
        
        <div class="status" id="status">Ready to test skeleton binding...</div>
        
        <div class="info-grid">
            <div class="info-box">
                <h4>Character Model</h4>
                <p id="character-info">Not loaded</p>
            </div>
            <div class="info-box">
                <h4>Cloth Model</h4>
                <p id="cloth-info">Not loaded</p>
            </div>
        </div>
        
        <div>
            <button id="load-character">Load Character Model</button>
            <button id="load-cloth" disabled>Load Cloth Model</button>
            <button id="test-binding" disabled>Test Skeleton Binding</button>
            <button id="play-anim" disabled>Play Animation</button>
            <button id="clear-log">Clear Log</button>
        </div>
        
        <div class="canvas-container">
            <canvas id="test-canvas" style="width: 100%; height: 100%;"></canvas>
        </div>
        
        <div id="log" class="log">Click 'Load Character Model' to start testing...</div>
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
                "three/examples/jsm/loaders/GLTFLoader.js": "https://unpkg.com/three@0.158.0/examples/jsm/loaders/GLTFLoader.js",
                "three/examples/jsm/controls/OrbitControls.js": "https://unpkg.com/three@0.158.0/examples/jsm/controls/OrbitControls.js"
            }
        }
    </script>
    <script type="module">
        import * as THREE from 'three';
        import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
        import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';

        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('status');
        const characterInfoElement = document.getElementById('character-info');
        const clothInfoElement = document.getElementById('cloth-info');
        
        let scene, camera, renderer, controls;
        let characterModel = null;
        let clothModel = null;
        let mixer = null;
        
        // 初始化3D场景
        function initScene() {
            const canvas = document.getElementById('test-canvas');
            
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x222222);
            
            camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
            camera.position.set(0, 0, 200);
            
            renderer = new THREE.WebGLRenderer({ canvas, antialias: true });
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
            renderer.shadowMap.enabled = true;
            
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            
            // 添加光源
            const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(50, 50, 50);
            scene.add(directionalLight);
            
            // 渲染循环
            function animate() {
                requestAnimationFrame(animate);
                controls.update();
                
                if (mixer) {
                    mixer.update(0.016); // 60fps
                }
                
                renderer.render(scene, camera);
            }
            animate();
            
            log('✅ 3D场景初始化完成');
        }
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateStatus(message) {
            statusElement.textContent = message;
            log(`STATUS: ${message}`);
        }

        function clearLog() {
            logElement.textContent = '';
        }

        // 加载角色模型
        async function loadCharacterModel() {
            updateStatus('Loading character model...');
            
            try {
                const loader = new GLTFLoader();
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load('/models/afdbda001.glb', resolve, undefined, reject);
                });
                
                characterModel = gltf.scene;
                characterModel.scale.set(100, 100, 100);
                characterModel.position.set(0, -75, 0);
                
                scene.add(characterModel);
                
                // 分析角色骨骼
                let skeletonInfo = analyzeModel(characterModel, 'Character');
                characterInfoElement.innerHTML = skeletonInfo;
                
                document.getElementById('load-cloth').disabled = false;
                updateStatus('Character model loaded successfully!');

            } catch (error) {
                log(`❌ Character model loading failed: ${error.message}`);
                updateStatus('Character model loading failed!');
            }
        }

        // 加载衣服模型
        async function loadClothModel() {
            updateStatus('Loading cloth model...');
            
            try {
                const loader = new GLTFLoader();
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load('/cloth/AFdWbA001_wb/afdwba001_wb.glb', resolve, undefined, reject);
                });
                
                clothModel = gltf.scene;
                clothModel.scale.set(100, 100, 100);
                clothModel.position.set(0, -75, 0);
                
                scene.add(clothModel);
                
                // 分析衣服骨骼
                let skeletonInfo = analyzeModel(clothModel, 'Cloth');
                clothInfoElement.innerHTML = skeletonInfo;
                
                document.getElementById('test-binding').disabled = false;
                updateStatus('Cloth model loaded successfully!');
                
            } catch (error) {
                log(`❌ Cloth model loading failed: ${error.message}`);
                updateStatus('Cloth model loading failed!');
            }
        }

        // 分析模型骨骼信息
        function analyzeModel(model, type) {
            let skinnedMeshCount = 0;
            let skeletonInfo = null;
            let boneNames = [];
            
            model.traverse((node) => {
                if (node.isSkinnedMesh) {
                    skinnedMeshCount++;
                    if (node.skeleton && !skeletonInfo) {
                        skeletonInfo = node.skeleton;
                        boneNames = node.skeleton.bones.map(bone => bone.name);
                    }
                    log(`${type} SkinnedMesh: ${node.name}`);
                }
            });
            
            if (skeletonInfo) {
                log(`${type} Skeleton: ${boneNames.length} bones`);
                log(`${type} Bone names: ${boneNames.slice(0, 5).join(', ')}${boneNames.length > 5 ? '...' : ''}`);
            }
            
            return `
                <strong>SkinnedMesh Count:</strong> ${skinnedMeshCount}<br>
                <strong>Bones Count:</strong> ${boneNames.length}<br>
                <strong>Sample Bones:</strong> ${boneNames.slice(0, 3).join(', ')}${boneNames.length > 3 ? '...' : ''}
            `;
        }

        // 测试骨骼绑定
        function testSkeletonBinding() {
            if (!characterModel || !clothModel) {
                log('❌ 请先加载角色和衣服模型');
                return;
            }
            
            updateStatus('Testing skeleton binding...');
            
            try {
                // 获取角色骨骼
                let characterSkeleton = null;
                characterModel.traverse((node) => {
                    if (node.isSkinnedMesh && node.skeleton) {
                        characterSkeleton = node.skeleton;
                    }
                });
                
                if (!characterSkeleton) {
                    log('❌ 角色模型中未找到骨骼系统');
                    return;
                }
                
                // 绑定衣服骨骼
                let bindCount = 0;
                clothModel.traverse((node) => {
                    if (node.isSkinnedMesh) {
                        try {
                            // 简单的骨骼绑定测试
                            const newBones = [];
                            const clothBones = node.skeleton.bones;
                            const characterBones = characterSkeleton.bones;
                            
                            // 创建骨骼映射
                            for (let clothBone of clothBones) {
                                const matchedBone = characterBones.find(cb => cb.name === clothBone.name);
                                if (matchedBone) {
                                    newBones.push(matchedBone);
                                } else {
                                    newBones.push(clothBone); // 使用原骨骼
                                }
                            }
                            
                            // 创建新骨骼系统
                            const newSkeleton = new THREE.Skeleton(newBones);
                            node.bind(newSkeleton, node.matrixWorld);
                            
                            bindCount++;
                            log(`✅ 成功绑定: ${node.name}`);
                            
                        } catch (error) {
                            log(`❌ 绑定失败 ${node.name}: ${error.message}`);
                        }
                    }
                });
                
                if (bindCount > 0) {
                    document.getElementById('play-anim').disabled = false;
                    updateStatus(`Skeleton binding completed! Bound ${bindCount} meshes.`);
                } else {
                    updateStatus('Skeleton binding failed!');
                }
                
            } catch (error) {
                log(`❌ 骨骼绑定测试失败: ${error.message}`);
                updateStatus('Skeleton binding test failed!');
            }
        }

        // 播放动画
        async function playAnimation() {
            if (!characterModel) {
                log('❌ 请先加载角色模型');
                return;
            }
            
            updateStatus('Loading and playing animation...');
            
            try {
                const loader = new GLTFLoader();
                
                const gltf = await new Promise((resolve, reject) => {
                    loader.load('/anim/AFdZAni_idle_000_v06_fixed/afdzani_idle_000_v06_fixed.glb', resolve, undefined, reject);
                });
                
                if (gltf.animations && gltf.animations.length > 0) {
                    mixer = new THREE.AnimationMixer(characterModel);
                    const action = mixer.clipAction(gltf.animations[0]);
                    action.setLoop(THREE.LoopRepeat);
                    action.play();
                    
                    log(`🎬 动画开始播放: ${gltf.animations[0].name}`);
                    updateStatus('Animation playing! Watch the character and cloth move together.');
                } else {
                    log('❌ 动画文件中未找到动画数据');
                }
                
            } catch (error) {
                log(`❌ 动画加载失败: ${error.message}`);
                updateStatus('Animation loading failed!');
            }
        }

        // 绑定事件监听器
        function bindEventListeners() {
            document.getElementById('load-character').addEventListener('click', loadCharacterModel);
            document.getElementById('load-cloth').addEventListener('click', loadClothModel);
            document.getElementById('test-binding').addEventListener('click', testSkeletonBinding);
            document.getElementById('play-anim').addEventListener('click', playAnimation);
            document.getElementById('clear-log').addEventListener('click', clearLog);
        }

        // 初始化
        initScene();
        bindEventListeners();
        log('🦴 Skeleton Binding Test page loaded. Ready for testing.');
    </script>
</body>
</html>
