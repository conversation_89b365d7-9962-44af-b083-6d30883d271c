// 简单的GLB加载器测试脚本
import { ModelLoader } from './sdk/loader/ModelLoader.js';

console.log('Starting GLB loader test...');

async function testGLBLoader() {
    try {
        const loader = new ModelLoader();
        
        console.log('Testing FBX format detection...');
        const fbxFormat = loader.getFileFormat('models/test.fbx');
        console.log('FBX format:', fbxFormat);
        
        console.log('Testing GLB format detection...');
        const glbFormat = loader.getFileFormat('models/test.glb');
        console.log('GLB format:', glbFormat);
        
        console.log('Testing GLTF format detection...');
        const gltfFormat = loader.getFileFormat('models/test.gltf');
        console.log('GLTF format:', gltfFormat);
        
        console.log('Format detection tests passed!');
        
        // 测试实际加载GLB模型
        console.log('Testing GLB model loading...');
        const model = await loader.load('models/afdbda001.glb');
        console.log('GLB model loaded successfully:', model);
        console.log('Model type:', model.type);
        console.log('Model children count:', model.children.length);
        
        return true;
    } catch (error) {
        console.error('GLB loader test failed:', error);
        return false;
    }
}

// 运行测试
testGLBLoader().then(success => {
    if (success) {
        console.log('✅ All GLB loader tests passed!');
    } else {
        console.log('❌ GLB loader tests failed!');
    }
});
